import { useCallback, useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { ILecture } from '@/redux/features/lecture/lectureSlice';
import { useRealTime } from './useRealTime';

interface OptimisticLectureUpdate {
  id: string;
  type: 'create' | 'update' | 'delete' | 'reorder';
  timestamp: number;
  originalData?: ILecture | ILecture[];
  tempId?: string;
}

interface UseLectureOptimisticUpdatesOptions {
  courseId: string;
  onLecturesChange: (lectures: ILecture[]) => void;
  onCourseUpdate?: (courseData: any) => void;
  enableRealTime?: boolean;
}

export const useLectureOptimisticUpdates = ({
  courseId,
  onLecturesChange,
  onCourseUpdate,
  enableRealTime = true
}: UseLectureOptimisticUpdatesOptions) => {
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, OptimisticLectureUpdate>>(new Map());
  const [isOptimisticLoading, setIsOptimisticLoading] = useState(false);
  const lecturesRef = useRef<ILecture[]>([]);

  // Real-time SSE/Polling connection for lecture and course updates
  const realTime = useRealTime({
    preferSSE: true,
    enablePollingFallback: true,
    pollingTypes: ['lecture_update', 'course_update'],
    autoConnect: enableRealTime,
    onUpdate: (update) => {
      // Handle lecture updates
      if (update.type === 'lecture_update' && update.data.courseId === courseId) {
        handleRealTimeUpdate(update.data);
      }
      // Handle course updates that affect lecture views
      if (update.type === 'course_update' && update.data.courseId === courseId) {
        handleCourseUpdate(update.data);
      }
    }
  });

  // Join course-specific room for real-time updates via HTTP API
  useEffect(() => {
    if (realTime.isConnected && courseId) {
      // Join room via HTTP API call
      joinCourseRoom(courseId);

      return () => {
        // Leave room via HTTP API call
        leaveCourseRoom(courseId);
      };
    }
  }, [realTime.isConnected, courseId]);

  // HTTP API functions for room management
  const joinCourseRoom = useCallback(async (courseId: string) => {
    try {
      // This would be implemented as an HTTP API call
      console.log(`Joining course room: ${courseId}`);
      // await api.post('/sse/join-room', { roomName: `course:${courseId}` });
    } catch (error) {
      console.error('Failed to join course room:', error);
    }
  }, []);

  const leaveCourseRoom = useCallback(async (courseId: string) => {
    try {
      // This would be implemented as an HTTP API call
      console.log(`Leaving course room: ${courseId}`);
      // await api.post('/sse/leave-room', { roomName: `course:${courseId}` });
    } catch (error) {
      console.error('Failed to leave course room:', error);
    }
  }, []);

  // Handle real-time course updates that affect lecture views
  const handleCourseUpdate = useCallback((data: any) => {
    const { action, course } = data;

    if (action === 'updated' && course && onCourseUpdate) {
      onCourseUpdate(course);
      toast.success('Course updated!', {
        description: `Course information has been updated.`
      });
    }
  }, [onCourseUpdate]);

  // Handle real-time updates from WebSocket
  const handleRealTimeUpdate = useCallback((data: any) => {
    const { action, lecture, lectures } = data;

    switch (action) {
      case 'created':
        if (lecture) {
          const updatedLectures = [...lecturesRef.current, lecture];
          lecturesRef.current = updatedLectures;
          onLecturesChange(updatedLectures);
          toast.success('New lecture added!', {
            description: `"${lecture.lectureTitle}" has been created.`
          });
        }
        break;
      case 'updated':
        if (lecture) {
          const updatedLectures = lecturesRef.current.map(l =>
            l._id === lecture._id ? { ...l, ...lecture } : l
          );
          lecturesRef.current = updatedLectures;
          onLecturesChange(updatedLectures);
          toast.success('Lecture updated!', {
            description: `"${lecture.lectureTitle}" has been updated.`
          });
        }
        break;
      case 'deleted':
        if (lecture?._id) {
          const updatedLectures = lecturesRef.current.filter(l => l._id !== lecture._id);
          lecturesRef.current = updatedLectures;
          onLecturesChange(updatedLectures);
          toast.success('Lecture deleted!', {
            description: `Lecture has been removed.`
          });
        }
        break;
      case 'reordered':
        if (lectures) {
          lecturesRef.current = lectures;
          onLecturesChange(lectures);
          toast.success('Lectures reordered!');
        }
        break;
    }
  }, [courseId, onLecturesChange]);

  // Update lectures reference when external changes occur
  useEffect(() => {
    return () => {
      lecturesRef.current = [];
    };
  }, []);

  // Optimistic create lecture
  const optimisticCreateLecture = useCallback(async (
    lectureData: Partial<ILecture>,
    apiCall: () => Promise<ILecture>
  ) => {
    const tempId = `temp-${Date.now()}`;
    const optimisticLecture: ILecture = {
      _id: tempId,
      lectureTitle: lectureData.lectureTitle || 'New Lecture',
      instruction: lectureData.instruction || '',
      videoUrl: lectureData.videoUrl || '',
      pdfUrl: lectureData.pdfUrl || '',
      isPreviewFree: lectureData.isPreviewFree || false,
      courseId: courseId,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...lectureData
    };

    // Add optimistic lecture to list
    const updatedLectures = [...lecturesRef.current, optimisticLecture];
    lecturesRef.current = updatedLectures;
    onLecturesChange(updatedLectures);

    // Track pending update
    const update: OptimisticLectureUpdate = {
      id: tempId,
      type: 'create',
      timestamp: Date.now(),
      tempId
    };
    setPendingUpdates(prev => new Map(prev).set(tempId, update));
    setIsOptimisticLoading(true);

    // Show immediate feedback
    toast.loading('Creating lecture...', { id: tempId });

    try {
      const createdLecture = await apiCall();
      
      // Replace optimistic lecture with real one
      const finalLectures = lecturesRef.current.map(lecture =>
        lecture._id === tempId ? createdLecture : lecture
      );
      lecturesRef.current = finalLectures;
      onLecturesChange(finalLectures);
      
      toast.success('Lecture created successfully!', { id: tempId });
      
      // Real-time updates are now handled automatically by the backend
      // No need to manually send messages - the backend will broadcast updates
      
      return createdLecture;
    } catch (error) {
      // Revert optimistic update
      const revertedLectures = lecturesRef.current.filter(lecture => lecture._id !== tempId);
      lecturesRef.current = revertedLectures;
      onLecturesChange(revertedLectures);
      
      toast.error('Failed to create lecture', { id: tempId });
      throw error;
    } finally {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
      setIsOptimisticLoading(false);
    }
  }, [courseId, onLecturesChange]);

  // Optimistic update lecture
  const optimisticUpdateLecture = useCallback(async (
    lectureId: string,
    updates: Partial<ILecture>,
    apiCall: () => Promise<ILecture>
  ) => {
    const originalLecture = lecturesRef.current.find(l => l._id === lectureId);
    if (!originalLecture) return;

    // Apply optimistic update
    const updatedLectures = lecturesRef.current.map(lecture =>
      lecture._id === lectureId 
        ? { ...lecture, ...updates, updatedAt: new Date() }
        : lecture
    );
    lecturesRef.current = updatedLectures;
    onLecturesChange(updatedLectures);

    // Track pending update
    const update: OptimisticLectureUpdate = {
      id: lectureId,
      type: 'update',
      originalData: originalLecture,
      timestamp: Date.now()
    };
    setPendingUpdates(prev => new Map(prev).set(lectureId, update));
    setIsOptimisticLoading(true);

    toast.loading('Updating lecture...', { id: lectureId });

    try {
      const updatedLecture = await apiCall();
      
      // Replace with server response
      const finalLectures = lecturesRef.current.map(lecture =>
        lecture._id === lectureId ? updatedLecture : lecture
      );
      lecturesRef.current = finalLectures;
      onLecturesChange(finalLectures);
      
      toast.success('Lecture updated successfully!', { id: lectureId });
      
      // Real-time updates are now handled automatically by the backend
      
      return updatedLecture;
    } catch (error) {
      // Revert optimistic update
      const revertedLectures = lecturesRef.current.map(lecture =>
        lecture._id === lectureId ? originalLecture : lecture
      );
      lecturesRef.current = revertedLectures;
      onLecturesChange(revertedLectures);
      
      toast.error('Failed to update lecture', { id: lectureId });
      throw error;
    } finally {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(lectureId);
        return newMap;
      });
      setIsOptimisticLoading(false);
    }
  }, [onLecturesChange, courseId]);

  // Optimistic delete lecture
  const optimisticDeleteLecture = useCallback(async (
    lectureId: string,
    apiCall: () => Promise<void>
  ) => {
    const originalLecture = lecturesRef.current.find(l => l._id === lectureId);
    if (!originalLecture) return;

    // Remove lecture optimistically
    const updatedLectures = lecturesRef.current.filter(lecture => lecture._id !== lectureId);
    lecturesRef.current = updatedLectures;
    onLecturesChange(updatedLectures);

    // Track pending update
    const update: OptimisticLectureUpdate = {
      id: lectureId,
      type: 'delete',
      originalData: originalLecture,
      timestamp: Date.now()
    };
    setPendingUpdates(prev => new Map(prev).set(lectureId, update));
    setIsOptimisticLoading(true);

    toast.loading('Deleting lecture...', { id: lectureId });

    try {
      await apiCall();

      toast.success('Lecture deleted successfully!', { id: lectureId });

      // Real-time updates are now handled automatically by the backend
    } catch (error) {
      // Revert optimistic delete
      const revertedLectures = [...lecturesRef.current, originalLecture];
      lecturesRef.current = revertedLectures;
      onLecturesChange(revertedLectures);

      toast.error('Failed to delete lecture', { id: lectureId });
      throw error;
    } finally {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(lectureId);
        return newMap;
      });
      setIsOptimisticLoading(false);
    }
  }, [onLecturesChange, courseId]);

  // Optimistic reorder lectures
  const optimisticReorderLectures = useCallback(async (
    newOrder: ILecture[],
    apiCall: () => Promise<ILecture[]>
  ) => {
    const originalLectures = [...lecturesRef.current];

    // Apply optimistic reorder
    lecturesRef.current = newOrder;
    onLecturesChange(newOrder);

    // Track pending update
    const updateId = `reorder-${Date.now()}`;
    const update: OptimisticLectureUpdate = {
      id: updateId,
      type: 'reorder',
      originalData: originalLectures,
      timestamp: Date.now()
    };
    setPendingUpdates(prev => new Map(prev).set(updateId, update));
    setIsOptimisticLoading(true);

    toast.loading('Reordering lectures...', { id: updateId });

    try {
      const reorderedLectures = await apiCall();

      lecturesRef.current = reorderedLectures;
      onLecturesChange(reorderedLectures);

      toast.success('Lectures reordered successfully!', { id: updateId });

      // Real-time updates are now handled automatically by the backend

      return reorderedLectures;
    } catch (error) {
      // Revert optimistic reorder
      lecturesRef.current = originalLectures;
      onLecturesChange(originalLectures);

      toast.error('Failed to reorder lectures', { id: updateId });
      throw error;
    } finally {
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(updateId);
        return newMap;
      });
      setIsOptimisticLoading(false);
    }
  }, [onLecturesChange, courseId]);

  // Set lectures reference for external updates
  const setLecturesRef = useCallback((lectures: ILecture[]) => {
    lecturesRef.current = lectures;
  }, []);

  // Clear all pending updates (useful for cleanup)
  const clearPendingUpdates = useCallback(() => {
    setPendingUpdates(new Map());
    setIsOptimisticLoading(false);
  }, []);

  return {
    optimisticCreateLecture,
    optimisticUpdateLecture,
    optimisticDeleteLecture,
    optimisticReorderLectures,
    setLecturesRef,
    clearPendingUpdates,
    pendingUpdates,
    isOptimisticLoading,
    isRealTimeConnected: realTime.isConnected,
    hasPendingUpdates: pendingUpdates.size > 0
  };
};
