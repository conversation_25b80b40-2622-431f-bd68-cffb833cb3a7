import { renderHook, act } from '@testing-library/react';
import { useRealTime } from '../useRealTime';
import { useServerSentEvents } from '../useServerSentEvents';
import { usePolling } from '../usePolling';

// Mock the dependent hooks
jest.mock('../useServerSentEvents');
jest.mock('../usePolling');

const mockUseServerSentEvents = useServerSentEvents as jest.MockedFunction<typeof useServerSentEvents>;
const mockUsePolling = usePolling as jest.MockedFunction<typeof usePolling>;

describe('useRealTime', () => {
  const mockSSEHook = {
    isConnected: false,
    isConnecting: false,
    connectionError: null,
    reconnectAttempts: 0,
    lastMessage: null,
    connectionTime: null,
    messagesReceived: 0,
    connect: jest.fn(),
    disconnect: jest.fn(),
    reconnect: jest.fn(),
    sendMessage: jest.fn(),
    joinRoom: jest.fn(),
    leaveRoom: jest.fn()
  };

  const mockPollingHook = {
    isActive: false,
    subscriptionId: null,
    lastPollTime: null,
    nextPollTime: null,
    updatesReceived: 0,
    errorCount: 0,
    currentInterval: 5000,
    start: jest.fn(),
    stop: jest.fn(),
    triggerPoll: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseServerSentEvents.mockReturnValue(mockSSEHook);
    mockUsePolling.mockReturnValue(mockPollingHook);
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useRealTime());

    expect(result.current.isConnected).toBe(false);
    expect(result.current.connectionMethod).toBe('none');
    expect(result.current.connectionQuality).toBe('offline');
  });

  it('should prefer SSE when available', () => {
    const { result } = renderHook(() => useRealTime({
      preferSSE: true,
      enablePollingFallback: true
    }));

    expect(mockUseServerSentEvents).toHaveBeenCalledWith(
      expect.objectContaining({
        autoConnect: true
      })
    );
  });

  it('should handle SSE connection success', () => {
    mockUseServerSentEvents.mockReturnValue({
      ...mockSSEHook,
      isConnected: true
    });

    const { result } = renderHook(() => useRealTime());

    expect(result.current.isConnected).toBe(true);
    expect(result.current.connectionMethod).toBe('sse');
    expect(result.current.sseStatus).toBe('connected');
  });

  it('should handle polling fallback when SSE fails', () => {
    mockUseServerSentEvents.mockReturnValue({
      ...mockSSEHook,
      connectionError: 'Connection failed'
    });

    mockUsePolling.mockReturnValue({
      ...mockPollingHook,
      isActive: true
    });

    const { result } = renderHook(() => useRealTime({
      preferSSE: true,
      enablePollingFallback: true
    }));

    expect(result.current.connectionMethod).toBe('polling');
    expect(result.current.pollingStatus).toBe('active');
  });

  it('should handle both SSE and polling active', () => {
    mockUseServerSentEvents.mockReturnValue({
      ...mockSSEHook,
      isConnected: true
    });

    mockUsePolling.mockReturnValue({
      ...mockPollingHook,
      isActive: true
    });

    const { result } = renderHook(() => useRealTime());

    expect(result.current.connectionMethod).toBe('both');
    expect(result.current.connectionQuality).toBe('excellent');
  });

  it('should filter duplicate updates', () => {
    const onUpdate = jest.fn();
    const { result } = renderHook(() => useRealTime({ onUpdate }));

    const update = {
      id: 'test-update-1',
      type: 'test',
      data: { message: 'Test' },
      timestamp: '2023-01-01T00:00:00Z',
      priority: 'medium' as const,
      source: 'sse' as const
    };

    // Simulate receiving the same update twice
    act(() => {
      const sseCallback = mockUseServerSentEvents.mock.calls[0][0].onMessage;
      sseCallback(update);
      sseCallback(update); // Duplicate
    });

    expect(onUpdate).toHaveBeenCalledTimes(1);
  });

  it('should handle connection quality degradation', () => {
    jest.useFakeTimers();

    const { result } = renderHook(() => useRealTime());

    // Initially excellent connection
    mockUseServerSentEvents.mockReturnValue({
      ...mockSSEHook,
      isConnected: true
    });

    mockUsePolling.mockReturnValue({
      ...mockPollingHook,
      isActive: true
    });

    // Fast forward time to trigger quality degradation
    act(() => {
      jest.advanceTimersByTime(60000); // 1 minute
    });

    // Quality should degrade if no updates received
    expect(result.current.connectionQuality).toBeDefined();

    jest.useRealTimers();
  });

  it('should provide manual connection controls', () => {
    const { result } = renderHook(() => useRealTime());

    act(() => {
      result.current.connect();
    });

    expect(mockSSEHook.connect).toHaveBeenCalled();
    expect(mockPollingHook.start).toHaveBeenCalled();

    act(() => {
      result.current.disconnect();
    });

    expect(mockSSEHook.disconnect).toHaveBeenCalled();
    expect(mockPollingHook.stop).toHaveBeenCalled();
  });

  it('should handle reconnection', () => {
    const { result } = renderHook(() => useRealTime());

    act(() => {
      result.current.reconnect();
    });

    expect(mockSSEHook.disconnect).toHaveBeenCalled();
    expect(mockPollingHook.stop).toHaveBeenCalled();

    // Should reconnect after delay
    setTimeout(() => {
      expect(mockSSEHook.connect).toHaveBeenCalled();
      expect(mockPollingHook.start).toHaveBeenCalled();
    }, 1100);
  });

  it('should expose individual hook methods', () => {
    const { result } = renderHook(() => useRealTime());

    expect(result.current.sse).toBeDefined();
    expect(result.current.sse.isConnected).toBe(false);
    expect(result.current.sse.reconnect).toBe(mockSSEHook.reconnect);

    expect(result.current.polling).toBeDefined();
    expect(result.current.polling.isActive).toBe(false);
    expect(result.current.polling.triggerPoll).toBe(mockPollingHook.triggerPoll);
  });

  it('should handle connection change callbacks', () => {
    const onConnectionChange = jest.fn();
    
    renderHook(() => useRealTime({ onConnectionChange }));

    // Should call callback when connection state changes
    expect(onConnectionChange).toHaveBeenCalled();
  });

  it('should handle update callbacks with filtering', () => {
    const onUpdate = jest.fn();
    
    renderHook(() => useRealTime({ 
      onUpdate,
      pollingTypes: ['specific-type']
    }));

    const relevantUpdate = {
      id: 'relevant-1',
      type: 'specific-type',
      data: { message: 'Relevant' },
      timestamp: '2023-01-01T00:00:00Z',
      priority: 'medium' as const,
      source: 'polling' as const
    };

    const irrelevantUpdate = {
      id: 'irrelevant-1',
      type: 'other-type',
      data: { message: 'Irrelevant' },
      timestamp: '2023-01-01T00:00:00Z',
      priority: 'medium' as const,
      source: 'polling' as const
    };

    act(() => {
      const pollingCallback = mockUsePolling.mock.calls[0][0].onUpdate;
      pollingCallback(relevantUpdate);
      pollingCallback(irrelevantUpdate);
    });

    expect(onUpdate).toHaveBeenCalledTimes(2); // Both should be passed through
  });

  it('should handle auto-connect configuration', () => {
    renderHook(() => useRealTime({ autoConnect: false }));

    expect(mockUseServerSentEvents).toHaveBeenCalledWith(
      expect.objectContaining({
        autoConnect: false
      })
    );

    expect(mockUsePolling).toHaveBeenCalledWith(
      expect.objectContaining({
        autoStart: false
      })
    );
  });

  it('should handle polling-only mode', () => {
    renderHook(() => useRealTime({ 
      preferSSE: false,
      enablePollingFallback: false
    }));

    expect(mockUseServerSentEvents).toHaveBeenCalledWith(
      expect.objectContaining({
        autoConnect: false
      })
    );

    expect(mockUsePolling).toHaveBeenCalledWith(
      expect.objectContaining({
        autoStart: true
      })
    );
  });
});
