import { Logger } from '../../config/logger';
import { jwtService } from './JWTService';
import config from '../../config';
import { JwtUserPayload } from '../../interface/auth';
import jwt from 'jsonwebtoken';

export interface SSEAuthResult {
  success: boolean;
  user?: JwtUserPayload;
  newTokens?: {
    accessToken: string;
    refreshToken: string;
  };
  error?: {
    type: 'token_expired' | 'token_invalid' | 'token_missing' | 'refresh_failed' | 'auth_failed';
    message: string;
    canRetry: boolean;
    retryAfter?: number;
    refreshRequired?: boolean;
  };
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // in milliseconds
  maxDelay: number; // in milliseconds
  backoffMultiplier: number;
}

export class SSEAuthService {
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2
  };

  private authAttempts: Map<string, { count: number; lastAttempt: number }> = new Map();
  private readonly ATTEMPT_WINDOW = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_ATTEMPTS_PER_WINDOW = 5; // Reduced from 10 to 5
  private readonly CIRCUIT_BREAKER_THRESHOLD = 3; // Circuit breaker after 3 failures
  private circuitBreakerState: Map<string, { isOpen: boolean; openedAt: number }> = new Map();

  /**
   * Calculate exponential backoff delay
   */
  private calculateBackoffDelay(attempt: number, config: RetryConfig = this.defaultRetryConfig): number {
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    return Math.min(delay, config.maxDelay);
  }

  /**
   * Check if client should be rate limited or circuit breaker is open
   */
  private shouldRateLimit(clientId: string): { limited: boolean; retryAfter?: number; reason?: string } {
    const now = Date.now();

    // Check circuit breaker first
    const circuitBreaker = this.circuitBreakerState.get(clientId);
    if (circuitBreaker?.isOpen) {
      const timeSinceOpened = now - circuitBreaker.openedAt;
      if (timeSinceOpened < 60000) { // 1 minute circuit breaker
        return {
          limited: true,
          retryAfter: Math.ceil((60000 - timeSinceOpened) / 1000),
          reason: 'circuit_breaker_open'
        };
      } else {
        // Reset circuit breaker after 1 minute
        this.circuitBreakerState.delete(clientId);
      }
    }

    const attempts = this.authAttempts.get(clientId);

    if (!attempts) {
      return { limited: false };
    }

    // Clean up old attempts
    if (now - attempts.lastAttempt > this.ATTEMPT_WINDOW) {
      this.authAttempts.delete(clientId);
      return { limited: false };
    }

    // Open circuit breaker if too many failures
    if (attempts.count >= this.CIRCUIT_BREAKER_THRESHOLD) {
      this.circuitBreakerState.set(clientId, { isOpen: true, openedAt: now });
      Logger.warn(`Circuit breaker opened for client: ${clientId} after ${attempts.count} failures`);
    }

    if (attempts.count >= this.MAX_ATTEMPTS_PER_WINDOW) {
      const retryAfter = Math.ceil((this.ATTEMPT_WINDOW - (now - attempts.lastAttempt)) / 1000);
      return { limited: true, retryAfter, reason: 'rate_limit' };
    }

    return { limited: false };
  }

  /**
   * Record authentication attempt
   */
  private recordAuthAttempt(clientId: string): void {
    const now = Date.now();
    const attempts = this.authAttempts.get(clientId);

    if (!attempts || now - attempts.lastAttempt > this.ATTEMPT_WINDOW) {
      this.authAttempts.set(clientId, { count: 1, lastAttempt: now });
    } else {
      attempts.count++;
      attempts.lastAttempt = now;
    }
  }

  /**
   * Authenticate SSE connection with retry and refresh logic
   */
  async authenticateSSEConnection(
    token: string,
    refreshToken?: string,
    clientId?: string,
    retryConfig?: Partial<RetryConfig>
  ): Promise<SSEAuthResult> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };
    const effectiveClientId = clientId || `anonymous_${Date.now()}`;

    // Check rate limiting
    const rateLimitCheck = this.shouldRateLimit(effectiveClientId);
    if (rateLimitCheck.limited) {
      Logger.warn(`SSE auth rate limited for client: ${effectiveClientId}`);
      return {
        success: false,
        error: {
          type: 'auth_failed',
          message: 'Too many authentication attempts. Please try again later.',
          canRetry: true,
          retryAfter: rateLimitCheck.retryAfter,
          refreshRequired: false
        }
      };
    }

    // Record this attempt
    this.recordAuthAttempt(effectiveClientId);

    try {
      // Attempt to verify the current token
      const result = await this.verifyTokenWithRefresh(token, refreshToken);
      
      if (result.success) {
        Logger.info(`SSE authentication successful for client: ${effectiveClientId}`);
        // Clear failed attempts on success
        this.authAttempts.delete(effectiveClientId);
      }

      return result;
    } catch (error) {
      Logger.error(`SSE authentication failed for client: ${effectiveClientId}:`, error);
      
      return {
        success: false,
        error: {
          type: 'auth_failed',
          message: error instanceof Error ? error.message : 'Authentication failed',
          canRetry: true,
          retryAfter: this.calculateBackoffDelay(1, config) / 1000,
          refreshRequired: true
        }
      };
    }
  }

  /**
   * Verify token with automatic refresh capability
   */
  private async verifyTokenWithRefresh(
    token: string,
    refreshToken?: string
  ): Promise<SSEAuthResult> {
    try {
      // First, try to verify the current token
      const decoded = await jwtService.verifyToken(token, config.jwt_access_secret);
      
      return {
        success: true,
        user: {
          _id: decoded._id || '',
          id: decoded.id || decoded._id || '',
          email: decoded.email,
          role: decoded.role as 'user' | 'teacher' | 'student',
          iat: decoded.iat,
          exp: decoded.exp
        }
      };
    } catch (error) {
      // Handle token expiration with refresh attempt
      if (error instanceof Error && error.name === 'TokenExpiredError') {
        Logger.info('Access token expired, attempting refresh...');
        
        if (refreshToken) {
          try {
            const newTokenPair = await jwtService.refreshTokens(refreshToken);
            const newDecoded = jwt.decode(newTokenPair.accessToken) as JwtUserPayload;
            
            Logger.info('Token refresh successful for SSE connection');
            return {
              success: true,
              user: {
                _id: newDecoded._id || '',
                id: newDecoded.id || newDecoded._id || '',
                email: newDecoded.email,
                role: newDecoded.role as 'user' | 'teacher' | 'student',
                iat: newDecoded.iat,
                exp: newDecoded.exp
              },
              newTokens: {
                accessToken: newTokenPair.accessToken,
                refreshToken: newTokenPair.refreshToken
              }
            };
          } catch (refreshError) {
            Logger.error('Token refresh failed:', refreshError);
            return {
              success: false,
              error: {
                type: 'refresh_failed',
                message: 'Token refresh failed. Please re-authenticate.',
                canRetry: false,
                refreshRequired: true
              }
            };
          }
        } else {
          return {
            success: false,
            error: {
              type: 'token_expired',
              message: 'Access token expired. Please refresh your authentication.',
              canRetry: true,
              retryAfter: 5,
              refreshRequired: true
            }
          };
        }
      }
      
      // Handle other JWT errors
      return {
        success: false,
        error: {
          type: 'token_invalid',
          message: error instanceof Error ? error.message : 'Invalid token',
          canRetry: false,
          refreshRequired: true
        }
      };
    }
  }

  /**
   * Get authentication statistics
   */
  getAuthStats(): {
    totalAttempts: number;
    activeClients: number;
    rateLimitedClients: number;
  } {
    const now = Date.now();
    let totalAttempts = 0;
    let rateLimitedClients = 0;

    for (const [clientId, attempts] of this.authAttempts.entries()) {
      if (now - attempts.lastAttempt <= this.ATTEMPT_WINDOW) {
        totalAttempts += attempts.count;
        if (attempts.count >= this.MAX_ATTEMPTS_PER_WINDOW) {
          rateLimitedClients++;
        }
      }
    }

    return {
      totalAttempts,
      activeClients: this.authAttempts.size,
      rateLimitedClients
    };
  }

  /**
   * Cleanup expired authentication attempts
   */
  cleanup(): void {
    const now = Date.now();
    const expiredClients: string[] = [];

    for (const [clientId, attempts] of this.authAttempts.entries()) {
      if (now - attempts.lastAttempt > this.ATTEMPT_WINDOW) {
        expiredClients.push(clientId);
      }
    }

    expiredClients.forEach(clientId => {
      this.authAttempts.delete(clientId);
    });

    if (expiredClients.length > 0) {
      Logger.debug(`Cleaned up ${expiredClients.length} expired auth attempts`);
    }
  }
}

// Export singleton instance
export const sseAuthService = new SSEAuthService();

// Setup periodic cleanup
setInterval(() => {
  sseAuthService.cleanup();
}, 5 * 60 * 1000); // Every 5 minutes
