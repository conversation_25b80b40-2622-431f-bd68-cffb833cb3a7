import React, { Suspense } from 'react';
import { useRealTimeDashboard } from '@/hooks/useRealTimeDashboard';
import { DashboardErrorBoundary, DashboardComponentErrorBoundary } from './DashboardErrorBoundary';
import { DashboardConnectionStatus } from './ConnectionStatus';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, Users, DollarSign, BookOpen } from 'lucide-react';
import { toast } from '@/utils/toast';

// Loading skeleton for dashboard cards
const DashboardCardSkeleton = () => (
  <Card>
    <CardHeader className="pb-2">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
      </div>
    </CardHeader>
    <CardContent>
      <Skeleton className="h-8 w-16 mb-2" />
      <Skeleton className="h-3 w-32" />
    </CardContent>
  </Card>
);

// Enterprise-level dashboard stats card with real-time updates
const DashboardStatsCard: React.FC<{
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  trend?: string;
  isLoading?: boolean;
  error?: string | null;
}> = ({ title, value, description, icon, trend, isLoading, error }) => {
  if (isLoading) return <DashboardCardSkeleton />;

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">Failed to load {title.toLowerCase()}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center gap-2 mt-1">
          <p className="text-xs text-muted-foreground">{description}</p>
          {trend && (
            <Badge variant="secondary" className="text-xs">
              {trend}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Main enterprise dashboard component
export const EnterpriseRealTimeDashboard: React.FC = () => {
  const {
    dashboardState,
    isRealTimeConnected,
    connectionStatus,
    initializeDashboard,
    refreshDashboard
  } = useRealTimeDashboard({
    enableOptimisticUpdates: true,
    enableRealTimeSync: true,
    pollingInterval: 30000
  });

  const { stats, isLoading, error } = dashboardState;

  // Show connection status notifications
  React.useEffect(() => {
    if (connectionStatus.isConnected && !isRealTimeConnected) {
      toast.success('Real-time updates connected', {
        description: 'You will now receive live updates for your dashboard.',
        duration: 3000
      });
    }
  }, [connectionStatus.isConnected, isRealTimeConnected]);

  return (
    <DashboardErrorBoundary>
      <div className="space-y-6">
        {/* Dashboard Header with Connection Status */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's what's happening with your courses today.
            </p>
          </div>
          
          <DashboardConnectionStatus realTimeHook={connectionStatus} />
        </div>

        {/* Connection Error Alert */}
        {connectionStatus.hasError && connectionStatus.connectionError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Real-time updates are currently unavailable: {connectionStatus.connectionError}
              {connectionStatus.reconnect && (
                <button 
                  onClick={connectionStatus.reconnect}
                  className="ml-2 underline hover:no-underline"
                >
                  Try reconnecting
                </button>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Dashboard Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <DashboardComponentErrorBoundary componentName="Total Courses">
            <DashboardStatsCard
              title="Total Courses"
              value={stats.totalCourses}
              description="Active courses"
              icon={<BookOpen className="h-4 w-4 text-muted-foreground" />}
              trend="+0%"
              isLoading={isLoading}
              error={error}
            />
          </DashboardComponentErrorBoundary>

          <DashboardComponentErrorBoundary componentName="Total Students">
            <DashboardStatsCard
              title="Total Students"
              value={stats.totalStudents}
              description="Enrolled students"
              icon={<Users className="h-4 w-4 text-muted-foreground" />}
              trend="+0%"
              isLoading={isLoading}
              error={error}
            />
          </DashboardComponentErrorBoundary>

          <DashboardComponentErrorBoundary componentName="Total Earnings">
            <DashboardStatsCard
              title="Total Earnings"
              value={`$${stats.totalEarnings}`}
              description="Total revenue"
              icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
              trend="+0%"
              isLoading={isLoading}
              error={error}
            />
          </DashboardComponentErrorBoundary>

          <DashboardComponentErrorBoundary componentName="Total Lectures">
            <DashboardStatsCard
              title="Total Lectures"
              value={stats.totalLectures}
              description="Published lectures"
              icon={<TrendingUp className="h-4 w-4 text-muted-foreground" />}
              trend="+0%"
              isLoading={isLoading}
              error={error}
            />
          </DashboardComponentErrorBoundary>
        </div>

        {/* Real-time Activity Feed */}
        <DashboardComponentErrorBoundary componentName="Activity Feed">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Recent Activity
                {isRealTimeConnected && (
                  <Badge variant="secondary" className="text-xs">
                    Live
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Real-time updates from your courses and students
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1 flex-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent activity</p>
                  <p className="text-sm">Activity will appear here as it happens</p>
                </div>
              )}
            </CardContent>
          </Card>
        </DashboardComponentErrorBoundary>

        {/* Debug Information (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="border-dashed">
            <CardHeader>
              <CardTitle className="text-sm">Debug Information</CardTitle>
            </CardHeader>
            <CardContent className="text-xs space-y-2">
              <div>Connection State: {connectionStatus.connectionState}</div>
              <div>Is Connected: {connectionStatus.isConnected ? 'Yes' : 'No'}</div>
              <div>Last Update: {connectionStatus.lastUpdate?.toLocaleTimeString() || 'Never'}</div>
              <div>Reconnect Attempts: {connectionStatus.reconnectAttempts}/{connectionStatus.maxReconnectAttempts}</div>
              {connectionStatus.connectionError && (
                <div className="text-red-600">Error: {connectionStatus.connectionError}</div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardErrorBoundary>
  );
};
