import { Router, Response } from 'express';
import { sseMiddlewareStack, sseError<PERSON><PERSON>ler, AuthenticatedSSERequest } from '../middlewares/sseMiddleware';
import { serviceRegistry } from '../services/ServiceRegistry';
import { Logger } from '../config/logger';
import { sseHealthMonitor } from '../services/sse/SSEHealthMonitor';
import { authAuditService } from '../services/audit/AuthAuditService';
import { sseAuthService } from '../services/auth/SSEAuthService';

const router = Router();

// Helper function to get SSE service (lazy loading)
const getSSEService = () => serviceRegistry.getSSEService();

/**
 * Main SSE connection endpoint
 * GET /api/sse/connect
 */
router.get('/connect', sseMiddlewareStack, (req: AuthenticatedSSERequest, res: Response) => {
  try {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const clientId = getSSEService().createConnection(
      req,
      res,
      req.user._id,
      req.user.role
    );

    Logger.info(`SSE connection established: ${clientId} for user ${req.user._id}`);
  } catch (error) {
    Logger.error('Failed to establish SSE connection:', error);
    sseErrorHandler(error, req, res, () => {});
  }
});

/**
 * Join room endpoint
 * POST /api/sse/join-room
 */
router.post('/join-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res: Response): void => {
  try {
    const { clientId, roomName } = req.body;

    if (!clientId || !roomName) {
      res.status(400).json({
        error: 'Missing required parameters',
        message: 'clientId and roomName are required'
      });
      return;
    }

    const success = getSSEService().joinRoom(clientId, roomName);

    if (success) {
      res.json({
        success: true,
        message: `Joined room: ${roomName}`,
        clientId,
        roomName
      });
    } else {
      res.status(404).json({
        error: 'Client not found',
        message: 'Invalid client ID or client not connected'
      });
    }
  } catch (error) {
    Logger.error('❌ Failed to join room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to join room'
    });
  }
});

/**
 * Leave room endpoint
 * POST /api/sse/leave-room
 */
router.post('/leave-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { clientId, roomName } = req.body;
    
    if (!clientId || !roomName) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'clientId and roomName are required'
      });
    }

    const success = getSSEService().leaveRoom(clientId, roomName);
    
    if (success) {
      res.json({
        success: true,
        message: `Left room: ${roomName}`,
        clientId,
        roomName
      });
    } else {
      res.status(404).json({
        error: 'Client not found',
        message: 'Invalid client ID or client not connected'
      });
    }
  } catch (error) {
    Logger.error('❌ Failed to leave room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to leave room'
    });
  }
});

/**
 * Send message to user endpoint
 * POST /api/sse/send-to-user
 */
router.post('/send-to-user', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { targetUserId, type, data, priority = 'medium' } = req.body;
    
    if (!targetUserId || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'targetUserId, type, and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      targetUserId,
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    const sentCount = getSSEService().sendToUser(targetUserId, message);
    
    res.json({
      success: true,
      message: 'Message sent successfully',
      sentToConnections: sentCount,
      messageId: message.id
    });
  } catch (error) {
    Logger.error('❌ Failed to send message to user:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to send message'
    });
  }
});

/**
 * Send message to room endpoint
 * POST /api/sse/send-to-room
 */
router.post('/send-to-room', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { roomName, type, data, priority = 'medium' } = req.body;
    
    if (!roomName || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'roomName, type, and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      room: roomName,
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    const sentCount = getSSEService().sendToRoom(roomName, message);
    
    res.json({
      success: true,
      message: 'Message sent to room successfully',
      sentToConnections: sentCount,
      messageId: message.id,
      roomName
    });
  } catch (error) {
    Logger.error('❌ Failed to send message to room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to send message to room'
    });
  }
});

/**
 * Broadcast message endpoint
 * POST /api/sse/broadcast
 */
router.post('/broadcast', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const { type, data, priority = 'medium', userType } = req.body;
    
    if (!type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'type and data are required'
      });
    }

    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      priority,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };

    let sentCount: number;
    
    if (userType && ['student', 'teacher', 'admin'].includes(userType)) {
      sentCount = getSSEService().sendToUserType(userType, message);
    } else {
      sentCount = getSSEService().broadcast(message);
    }
    
    res.json({
      success: true,
      message: userType ? `Message broadcast to ${userType}s successfully` : 'Message broadcast successfully',
      sentToConnections: sentCount,
      messageId: message.id,
      userType: userType || 'all'
    });
  } catch (error) {
    Logger.error('❌ Failed to broadcast message:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to broadcast message'
    });
  }
});

/**
 * Get connection statistics endpoint
 * GET /api/sse/stats
 */
router.get('/stats', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const stats = getSSEService().getConnectionStats();
    const connectedUsers = getSSEService().getConnectedUsers();
    
    res.json({
      success: true,
      data: {
        connectionStats: stats,
        connectedUsers: connectedUsers,
        timestamp: new Date()
      }
    });
  } catch (error) {
    Logger.error('❌ Failed to get SSE stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve statistics'
    });
  }
});

/**
 * Enhanced health check endpoint with comprehensive metrics
 * GET /api/sse/health
 */
router.get('/health', async (req, res) => {
  try {
    const stats = getSSEService().getConnectionStats();
    const healthMetrics = sseHealthMonitor.getHealthMetrics();
    const authMetrics = await authAuditService.getMetrics();
    const authStats = sseAuthService.getAuthStats();

    res.json({
      status: 'healthy',
      service: 'SSE Service',
      timestamp: new Date(),
      uptime: process.uptime(),
      connections: {
        total: stats.totalConnections,
        active: stats.activeConnections,
        healthy: healthMetrics.healthyConnections,
        unhealthy: healthMetrics.unhealthyConnections,
        byRole: healthMetrics.connectionsByRole,
        byStatus: healthMetrics.connectionsByStatus
      },
      performance: {
        averageLatency: healthMetrics.averageLatency,
        totalErrors: healthMetrics.totalErrors,
        totalReconnects: healthMetrics.totalReconnects
      },
      authentication: {
        totalAttempts: authMetrics.totalAuthAttempts,
        successfulAuths: authMetrics.successfulAuths,
        failedAuths: authMetrics.failedAuths,
        tokenRefreshes: authMetrics.tokenRefreshes,
        rateLimitViolations: authMetrics.rateLimitViolations,
        suspiciousActivities: authMetrics.suspiciousActivities,
        activeClients: authStats.activeClients,
        rateLimitedClients: authStats.rateLimitedClients
      }
    });
  } catch (error) {
    Logger.error('❌ SSE health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      service: 'SSE Service',
      error: 'Health check failed'
    });
  }
});

/**
 * Get detailed connection health information
 * GET /api/sse/health/connections
 */
router.get('/health/connections', sseMiddlewareStack, (req: AuthenticatedSSERequest, res) => {
  try {
    const connections = sseHealthMonitor.getAllConnectionsHealth();

    res.json({
      success: true,
      data: {
        connections,
        total: connections.length,
        timestamp: new Date()
      }
    });
  } catch (error) {
    Logger.error('❌ Failed to get connection health:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve connection health information'
    });
  }
});

/**
 * Get authentication audit events
 * GET /api/sse/health/audit
 */
router.get('/health/audit', sseMiddlewareStack, async (req: AuthenticatedSSERequest, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 100;
    const events = await authAuditService.getRecentEvents(limit);

    res.json({
      success: true,
      data: {
        events,
        total: events.length,
        timestamp: new Date()
      }
    });
  } catch (error) {
    Logger.error('❌ Failed to get audit events:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve audit events'
    });
  }
});

export default router;
