import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import PollingService from '../app/services/polling/PollingService';

describe('Polling Service', () => {
  let pollingService: PollingService;

  beforeEach(() => {
    pollingService = new PollingService();
  });

  afterEach(() => {
    pollingService.shutdown();
  });

  describe('Subscription Management', () => {
    it('should create polling subscription', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student', ['analytics', 'notifications']);
      
      expect(subscriptionId).toBeDefined();
      expect(subscriptionId).toMatch(/^poll_user123_\d+$/);
      
      const stats = pollingService.getStats();
      expect(stats.totalSubscriptions).toBe(1);
      expect(stats.activeSubscriptions).toBe(1);
    });

    it('should remove polling subscription', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      const success = pollingService.unsubscribe(subscriptionId);
      
      expect(success).toBe(true);
      
      const stats = pollingService.getStats();
      expect(stats.activeSubscriptions).toBe(0);
    });

    it('should handle unsubscribe of non-existent subscription', () => {
      const success = pollingService.unsubscribe('non-existent-id');
      expect(success).toBe(false);
    });

    it('should track subscription statistics', () => {
      pollingService.subscribe('user1', 'student');
      pollingService.subscribe('user2', 'teacher');
      pollingService.subscribe('user3', 'admin');
      
      const stats = pollingService.getStats();
      expect(stats.totalSubscriptions).toBe(3);
      expect(stats.activeSubscriptions).toBe(3);
    });
  });

  describe('Update Management', () => {
    let subscriptionId: string;

    beforeEach(() => {
      subscriptionId = pollingService.subscribe('user123', 'student', ['test', 'notifications']);
    });

    it('should add update for specific user', () => {
      const update = {
        id: 'update-1',
        type: 'test',
        data: { message: 'Test update' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      };

      pollingService.addUpdate(update);
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch).toBeDefined();
      expect(batch!.updates).toHaveLength(1);
      expect(batch!.updates[0].id).toBe('update-1');
    });

    it('should add multiple updates in batch', () => {
      const updates = [
        {
          id: 'batch-1',
          type: 'test',
          data: { message: 'Batch update 1' },
          timestamp: new Date(),
          userId: 'user123',
          priority: 'low' as const
        },
        {
          id: 'batch-2',
          type: 'test',
          data: { message: 'Batch update 2' },
          timestamp: new Date(),
          userId: 'user123',
          priority: 'medium' as const
        }
      ];

      pollingService.addBatchUpdates(updates);
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch).toBeDefined();
      expect(batch!.updates).toHaveLength(2);
    });

    it('should filter updates by subscription types', () => {
      // Add updates of different types
      pollingService.addUpdate({
        id: 'test-update',
        type: 'test',
        data: { message: 'Should be included' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      });

      pollingService.addUpdate({
        id: 'other-update',
        type: 'other',
        data: { message: 'Should be excluded' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      });

      const batch = pollingService.poll(subscriptionId);
      expect(batch).toBeDefined();
      expect(batch!.updates).toHaveLength(1);
      expect(batch!.updates[0].type).toBe('test');
    });

    it('should handle polling with lastUpdateId', () => {
      // Add first update
      pollingService.addUpdate({
        id: 'update-1',
        type: 'test',
        data: { message: 'First update' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      });

      // Poll and get first update
      const firstBatch = pollingService.poll(subscriptionId);
      expect(firstBatch!.updates).toHaveLength(1);

      // Add second update
      pollingService.addUpdate({
        id: 'update-2',
        type: 'test',
        data: { message: 'Second update' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      });

      // Poll with lastUpdateId
      const secondBatch = pollingService.poll(subscriptionId, 'update-1');
      expect(secondBatch!.updates).toHaveLength(1);
      expect(secondBatch!.updates[0].id).toBe('update-2');
    });
  });

  describe('Broadcasting', () => {
    it('should broadcast to user type', () => {
      const studentSub = pollingService.subscribe('student1', 'student', ['broadcast']);
      const teacherSub = pollingService.subscribe('teacher1', 'teacher', ['broadcast']);

      pollingService.broadcastToUserType('student', {
        type: 'broadcast',
        data: { message: 'Student broadcast' },
        priority: 'medium'
      });

      const studentBatch = pollingService.poll(studentSub);
      const teacherBatch = pollingService.poll(teacherSub);

      expect(studentBatch!.updates).toHaveLength(1);
      expect(teacherBatch!.updates).toHaveLength(0);
    });

    it('should broadcast to room', () => {
      const user1Sub = pollingService.subscribe('user1', 'student', ['room_update']);
      const user2Sub = pollingService.subscribe('user2', 'student', ['room_update']);

      pollingService.broadcastToRoom('room123', {
        type: 'room_update',
        data: { roomId: 'room123', message: 'Room update' },
        priority: 'medium'
      });

      const user1Batch = pollingService.poll(user1Sub);
      const user2Batch = pollingService.poll(user2Sub);

      expect(user1Batch!.updates).toHaveLength(1);
      expect(user2Batch!.updates).toHaveLength(1);
      expect(user1Batch!.updates[0].data.roomId).toBe('room123');
    });
  });

  describe('Exponential Backoff', () => {
    it('should calculate next poll time with backoff', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      
      // Simulate polling failure
      const subscription = (pollingService as any).subscriptions.get(subscriptionId);
      if (subscription) {
        (pollingService as any).handlePollingFailure(subscription);
        
        const nextPollTime = pollingService.getNextPollTime(subscriptionId);
        expect(nextPollTime).toBeDefined();
        expect(nextPollTime!.getTime()).toBeGreaterThan(Date.now());
      }
    });

    it('should deactivate subscription after max failures', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      const subscription = (pollingService as any).subscriptions.get(subscriptionId);
      
      if (subscription) {
        // Simulate multiple failures
        for (let i = 0; i < 6; i++) {
          (pollingService as any).handlePollingFailure(subscription);
        }
        
        expect(subscription.isActive).toBe(false);
      }
    });
  });

  describe('Cleanup and Maintenance', () => {
    it('should clean up expired updates', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      
      // Add update with expiration
      pollingService.addUpdate({
        id: 'expired-update',
        type: 'test',
        data: { message: 'Will expire' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'low' as const,
        expiresAt: new Date(Date.now() - 1000) // Already expired
      });

      // Trigger cleanup
      (pollingService as any).performCleanup();
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch!.updates).toHaveLength(0);
    });

    it('should clear updates for specific user', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      
      pollingService.addUpdate({
        id: 'user-update',
        type: 'test',
        data: { message: 'User update' },
        timestamp: new Date(),
        userId: 'user123',
        priority: 'medium' as const
      });

      pollingService.clearUpdatesForUser('user123');
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch!.updates).toHaveLength(0);
    });

    it('should clear global updates', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student', ['global']);
      
      pollingService.addUpdate({
        id: 'global-update',
        type: 'global',
        data: { message: 'Global update' },
        timestamp: new Date(),
        priority: 'medium' as const
      });

      pollingService.clearGlobalUpdates();
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch!.updates).toHaveLength(0);
    });
  });

  describe('Performance and Limits', () => {
    it('should limit batch size', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      
      // Add more updates than batch limit
      for (let i = 0; i < 100; i++) {
        pollingService.addUpdate({
          id: `update-${i}`,
          type: 'test',
          data: { index: i },
          timestamp: new Date(),
          userId: 'user123',
          priority: 'low' as const
        });
      }

      const batch = pollingService.poll(subscriptionId);
      expect(batch!.updates.length).toBeLessThanOrEqual(50); // MAX_UPDATES_PER_BATCH
      expect(batch!.hasMore).toBe(true);
    });

    it('should handle concurrent polling', () => {
      const subscriptions = [];
      const numSubscriptions = 50;

      // Create multiple subscriptions
      for (let i = 0; i < numSubscriptions; i++) {
        const subId = pollingService.subscribe(`user${i}`, 'student');
        subscriptions.push(subId);
      }

      // Add updates for all users
      for (let i = 0; i < numSubscriptions; i++) {
        pollingService.addUpdate({
          id: `concurrent-${i}`,
          type: 'test',
          data: { userId: `user${i}` },
          timestamp: new Date(),
          userId: `user${i}`,
          priority: 'medium' as const
        });
      }

      // Poll all subscriptions concurrently
      const batches = subscriptions.map(subId => pollingService.poll(subId));
      
      expect(batches).toHaveLength(numSubscriptions);
      batches.forEach((batch, index) => {
        expect(batch!.updates).toHaveLength(1);
        expect(batch!.updates[0].data.userId).toBe(`user${index}`);
      });
    });

    it('should maintain performance with large update queues', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      const numUpdates = 1000;
      
      const startTime = Date.now();
      
      // Add many updates
      for (let i = 0; i < numUpdates; i++) {
        pollingService.addUpdate({
          id: `perf-${i}`,
          type: 'test',
          data: { index: i },
          timestamp: new Date(),
          userId: 'user123',
          priority: 'low' as const
        });
      }
      
      const addTime = Date.now() - startTime;
      expect(addTime).toBeLessThan(1000); // Should complete within 1 second
      
      const pollStartTime = Date.now();
      const batch = pollingService.poll(subscriptionId);
      const pollTime = Date.now() - pollStartTime;
      
      expect(pollTime).toBeLessThan(100); // Polling should be fast
      expect(batch!.updates.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle polling non-existent subscription', () => {
      const batch = pollingService.poll('non-existent-subscription');
      expect(batch).toBeNull();
    });

    it('should handle malformed update data', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      
      // Add update with circular reference
      const circularData: any = { message: 'Test' };
      circularData.self = circularData;
      
      expect(() => {
        pollingService.addUpdate({
          id: 'circular-update',
          type: 'test',
          data: circularData,
          timestamp: new Date(),
          userId: 'user123',
          priority: 'medium' as const
        });
      }).not.toThrow();
      
      const batch = pollingService.poll(subscriptionId);
      expect(batch!.updates).toHaveLength(1);
    });

    it('should recover from polling errors', () => {
      const subscriptionId = pollingService.subscribe('user123', 'student');
      const subscription = (pollingService as any).subscriptions.get(subscriptionId);
      
      if (subscription) {
        // Simulate error
        subscription.failureCount = 2;
        
        // Should still be able to poll
        const batch = pollingService.poll(subscriptionId);
        expect(batch).toBeDefined();
        
        // Failure count should reset on successful poll
        expect(subscription.failureCount).toBe(0);
      }
    });
  });
});
