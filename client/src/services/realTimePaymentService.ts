import { toast } from 'sonner';
import { useRealTime } from '@/hooks/useRealTime';

export interface PaymentUpdate {
  type: 'payment_success' | 'payment_failed' | 'payout_processed' | 'earnings_update' | 'transaction_update';
  data: any;
  timestamp: string;
  userId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface PaymentNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  amount?: number;
  currency?: string;
  transactionId?: string;
  timestamp: Date;
  actionUrl?: string;
  actionText?: string;
}

export interface PaymentServiceStats {
  isConnected: boolean;
  connectionMethod: 'sse' | 'polling' | 'both' | 'none';
  notificationsReceived: number;
  lastNotification: PaymentNotification | null;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
}

class RealTimePaymentService {
  private static instance: RealTimePaymentService | null = null;
  private realTimeHook: any = null;
  private stats: PaymentServiceStats = {
    isConnected: false,
    connectionMethod: 'none',
    notificationsReceived: 0,
    lastNotification: null,
    connectionQuality: 'offline'
  };
  private listeners: Map<string, (update: PaymentUpdate) => void> = new Map();

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): RealTimePaymentService {
    if (!RealTimePaymentService.instance) {
      RealTimePaymentService.instance = new RealTimePaymentService();
    }
    return RealTimePaymentService.instance;
  }

  /**
   * Initialize the service with real-time connection
   */
  public initialize() {
    // This would be called from a React component that uses the useRealTime hook
    console.log('RealTimePaymentService initialized - use useRealTimePayments hook in components');
  }

  /**
   * Subscribe to payment updates
   */
  public subscribe(listenerId: string, callback: (update: PaymentUpdate) => void): void {
    this.listeners.set(listenerId, callback);
  }

  /**
   * Unsubscribe from payment updates
   */
  public unsubscribe(listenerId: string): void {
    this.listeners.delete(listenerId);
  }

  /**
   * Handle payment update
   */
  public handlePaymentUpdate(update: PaymentUpdate): void {
    this.stats.notificationsReceived++;
    
    // Create notification
    const notification = this.createNotificationFromUpdate(update);
    this.stats.lastNotification = notification;

    // Show toast notification
    this.showToastNotification(notification);

    // Notify all listeners
    for (const callback of this.listeners.values()) {
      try {
        callback(update);
      } catch (error) {
        console.error('Error in payment update listener:', error);
      }
    }

    // Dispatch custom event for components that listen to DOM events
    window.dispatchEvent(new CustomEvent('payment-update', {
      detail: update
    }));
  }

  /**
   * Get service statistics
   */
  public getStats(): PaymentServiceStats {
    return { ...this.stats };
  }

  /**
   * Update connection stats
   */
  public updateConnectionStats(stats: any): void {
    this.stats.isConnected = stats.isConnected;
    this.stats.connectionMethod = stats.connectionMethod;
    this.stats.connectionQuality = stats.connectionQuality;
  }

  /**
   * Private helper methods
   */
  private createNotificationFromUpdate(update: PaymentUpdate): PaymentNotification {
    const notification: PaymentNotification = {
      id: `payment_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type: 'info',
      title: 'Payment Update',
      message: 'Payment status updated',
      timestamp: new Date()
    };

    switch (update.type) {
      case 'payment_success':
        notification.type = 'success';
        notification.title = 'Payment Successful';
        notification.message = `Payment of ${update.data.amount} ${update.data.currency || 'USD'} was successful`;
        notification.amount = update.data.amount;
        notification.currency = update.data.currency;
        notification.transactionId = update.data.transactionId;
        break;

      case 'payment_failed':
        notification.type = 'error';
        notification.title = 'Payment Failed';
        notification.message = update.data.reason || 'Payment could not be processed';
        notification.amount = update.data.amount;
        notification.currency = update.data.currency;
        break;

      case 'payout_processed':
        notification.type = 'success';
        notification.title = 'Payout Processed';
        notification.message = `Payout of ${update.data.amount} ${update.data.currency || 'USD'} has been processed`;
        notification.amount = update.data.amount;
        notification.currency = update.data.currency;
        notification.actionUrl = '/dashboard/earnings';
        notification.actionText = 'View Earnings';
        break;

      case 'earnings_update':
        notification.type = 'info';
        notification.title = 'Earnings Updated';
        notification.message = `Your earnings have been updated`;
        notification.actionUrl = '/dashboard/earnings';
        notification.actionText = 'View Details';
        break;

      case 'transaction_update':
        notification.type = 'info';
        notification.title = 'Transaction Update';
        notification.message = `Transaction ${update.data.transactionId} status updated`;
        notification.transactionId = update.data.transactionId;
        break;

      default:
        notification.message = 'Payment system update received';
    }

    return notification;
  }

  private showToastNotification(notification: PaymentNotification): void {
    const toastOptions = {
      duration: notification.type === 'error' ? 8000 : 5000,
      action: notification.actionUrl ? {
        label: notification.actionText || 'View',
        onClick: () => window.open(notification.actionUrl, '_blank'),
      } : undefined,
    };

    switch (notification.type) {
      case 'success':
        toast.success(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      case 'error':
        toast.error(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      case 'warning':
        toast.warning(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      default:
        toast.info(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
    }
  }
}

// Export singleton instance
export const realTimePaymentService = RealTimePaymentService.getInstance();

/**
 * React hook for using real-time payment updates
 */
export const useRealTimePayments = () => {
  const realTime = useRealTime({
    preferSSE: true,
    enablePollingFallback: true,
    pollingTypes: ['payment_success', 'payment_failed', 'payout_processed', 'earnings_update', 'transaction_update'],
    autoConnect: true,
    onUpdate: (update) => {
      // Filter payment-related updates
      if (isPaymentUpdate(update)) {
        const paymentUpdate: PaymentUpdate = {
          type: update.type as PaymentUpdate['type'],
          data: update.data,
          timestamp: update.timestamp,
          userId: update.data.userId,
          priority: update.priority
        };

        realTimePaymentService.handlePaymentUpdate(paymentUpdate);
      }
    },
    onConnectionChange: (stats) => {
      realTimePaymentService.updateConnectionStats(stats);
    }
  });

  const subscribe = (listenerId: string, callback: (update: PaymentUpdate) => void) => {
    realTimePaymentService.subscribe(listenerId, callback);
  };

  const unsubscribe = (listenerId: string) => {
    realTimePaymentService.unsubscribe(listenerId);
  };

  const getStats = () => {
    return realTimePaymentService.getStats();
  };

  return {
    isConnected: realTime.isConnected,
    connectionMethod: realTime.connectionMethod,
    connectionQuality: realTime.connectionQuality,
    subscribe,
    unsubscribe,
    getStats,
    // Expose real-time connection details
    realTimeStats: realTime
  };
};

/**
 * Helper function to check if update is payment-related
 */
function isPaymentUpdate(update: any): boolean {
  const paymentTypes = [
    'payment_success', 'payment_failed', 'payout_processed', 
    'earnings_update', 'transaction_update'
  ];
  return paymentTypes.includes(update.type);
}

export default RealTimePaymentService;
