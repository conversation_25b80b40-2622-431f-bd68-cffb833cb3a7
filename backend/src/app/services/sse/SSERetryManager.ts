import { Logger } from '../../config/logger';
import { redisOperations } from '../../config/redis';

export interface RetryPolicy {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  jitterEnabled: boolean;
}

export interface RetryAttempt {
  attemptId: string;
  clientId: string;
  operation: 'auth' | 'connect' | 'heartbeat' | 'message';
  attemptNumber: number;
  timestamp: Date;
  error?: string;
  nextRetryAt?: Date;
  success?: boolean;
}

export interface RetryStats {
  totalRetries: number;
  successfulRetries: number;
  failedRetries: number;
  averageRetryDelay: number;
  operationStats: Record<string, {
    attempts: number;
    successes: number;
    failures: number;
  }>;
}

export class SSERetryManager {
  private readonly defaultPolicy: RetryPolicy = {
    maxRetries: 5,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
    jitterEnabled: true
  };

  private retryAttempts: Map<string, RetryAttempt[]> = new Map();
  private readonly REDIS_KEY_PREFIX = 'sse:retry:';
  private readonly RETRY_WINDOW = 10 * 60 * 1000; // 10 minutes

  /**
   * Calculate next retry delay with exponential backoff and jitter
   */
  calculateRetryDelay(
    attemptNumber: number,
    policy: RetryPolicy = this.defaultPolicy
  ): number {
    let delay = policy.baseDelay * Math.pow(policy.backoffMultiplier, attemptNumber - 1);
    
    // Apply maximum delay cap
    delay = Math.min(delay, policy.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (policy.jitterEnabled) {
      const jitter = Math.random() * 0.1 * delay; // ±10% jitter
      delay += (Math.random() > 0.5 ? jitter : -jitter);
    }
    
    return Math.max(delay, policy.baseDelay);
  }

  /**
   * Check if operation should be retried
   */
  shouldRetry(
    clientId: string,
    operation: string,
    policy: RetryPolicy = this.defaultPolicy
  ): { canRetry: boolean; retryAfter?: number; reason?: string } {
    const attempts = this.getClientAttempts(clientId, operation);
    
    if (attempts.length >= policy.maxRetries) {
      return {
        canRetry: false,
        reason: `Maximum retry attempts (${policy.maxRetries}) exceeded`
      };
    }

    // Check if we're within retry window
    const now = new Date();
    const recentAttempts = attempts.filter(
      attempt => now.getTime() - attempt.timestamp.getTime() < this.RETRY_WINDOW
    );

    if (recentAttempts.length >= policy.maxRetries) {
      const oldestAttempt = recentAttempts[0];
      const retryAfter = this.RETRY_WINDOW - (now.getTime() - oldestAttempt.timestamp.getTime());
      
      return {
        canRetry: false,
        retryAfter: Math.ceil(retryAfter / 1000),
        reason: 'Too many attempts within retry window'
      };
    }

    const nextDelay = this.calculateRetryDelay(attempts.length + 1, policy);
    
    return {
      canRetry: true,
      retryAfter: Math.ceil(nextDelay / 1000)
    };
  }

  /**
   * Record retry attempt
   */
  async recordRetryAttempt(
    clientId: string,
    operation: 'auth' | 'connect' | 'heartbeat' | 'message',
    error?: string,
    policy: RetryPolicy = this.defaultPolicy
  ): Promise<RetryAttempt> {
    const attempts = this.getClientAttempts(clientId, operation);
    const attemptNumber = attempts.length + 1;
    
    const attempt: RetryAttempt = {
      attemptId: this.generateAttemptId(),
      clientId,
      operation,
      attemptNumber,
      timestamp: new Date(),
      error,
      success: false
    };

    // Calculate next retry time
    if (attemptNumber < policy.maxRetries) {
      const delay = this.calculateRetryDelay(attemptNumber + 1, policy);
      attempt.nextRetryAt = new Date(Date.now() + delay);
    }

    // Store attempt
    if (!this.retryAttempts.has(clientId)) {
      this.retryAttempts.set(clientId, []);
    }
    this.retryAttempts.get(clientId)!.push(attempt);

    // Persist to Redis
    await this.persistRetryAttempt(attempt);

    Logger.warn(`🔄 Retry attempt recorded: ${operation} for client ${clientId} (attempt ${attemptNumber}/${policy.maxRetries})`);
    
    return attempt;
  }

  /**
   * Record successful operation (clears retry history)
   */
  async recordSuccess(clientId: string, operation: string): Promise<void> {
    const attempts = this.getClientAttempts(clientId, operation);
    
    if (attempts.length > 0) {
      // Mark last attempt as successful
      const lastAttempt = attempts[attempts.length - 1];
      lastAttempt.success = true;
      
      await this.persistRetryAttempt(lastAttempt);
      
      Logger.info(`✅ Operation successful after ${attempts.length} attempts: ${operation} for client ${clientId}`);
    }

    // Clear retry history for this operation
    this.clearClientAttempts(clientId, operation);
  }

  /**
   * Get retry attempts for a specific client and operation
   */
  private getClientAttempts(clientId: string, operation: string): RetryAttempt[] {
    const allAttempts = this.retryAttempts.get(clientId) || [];
    return allAttempts.filter(attempt => attempt.operation === operation);
  }

  /**
   * Clear retry attempts for a specific client and operation
   */
  private clearClientAttempts(clientId: string, operation: string): void {
    const allAttempts = this.retryAttempts.get(clientId) || [];
    const filteredAttempts = allAttempts.filter(attempt => attempt.operation !== operation);
    
    if (filteredAttempts.length === 0) {
      this.retryAttempts.delete(clientId);
    } else {
      this.retryAttempts.set(clientId, filteredAttempts);
    }
  }

  /**
   * Get retry statistics
   */
  getRetryStats(): RetryStats {
    const allAttempts = Array.from(this.retryAttempts.values()).flat();
    
    const stats: RetryStats = {
      totalRetries: allAttempts.length,
      successfulRetries: allAttempts.filter(a => a.success).length,
      failedRetries: allAttempts.filter(a => !a.success).length,
      averageRetryDelay: 0,
      operationStats: {}
    };

    // Calculate operation-specific stats
    const operationGroups = allAttempts.reduce((groups, attempt) => {
      if (!groups[attempt.operation]) {
        groups[attempt.operation] = [];
      }
      groups[attempt.operation].push(attempt);
      return groups;
    }, {} as Record<string, RetryAttempt[]>);

    Object.entries(operationGroups).forEach(([operation, attempts]) => {
      stats.operationStats[operation] = {
        attempts: attempts.length,
        successes: attempts.filter(a => a.success).length,
        failures: attempts.filter(a => !a.success).length
      };
    });

    return stats;
  }

  /**
   * Cleanup old retry attempts
   */
  cleanup(): void {
    const now = new Date();
    const cutoff = now.getTime() - this.RETRY_WINDOW;
    
    for (const [clientId, attempts] of this.retryAttempts.entries()) {
      const recentAttempts = attempts.filter(
        attempt => attempt.timestamp.getTime() > cutoff
      );
      
      if (recentAttempts.length === 0) {
        this.retryAttempts.delete(clientId);
      } else {
        this.retryAttempts.set(clientId, recentAttempts);
      }
    }
  }

  /**
   * Generate unique attempt ID
   */
  private generateAttemptId(): string {
    return `retry_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Persist retry attempt to Redis
   */
  private async persistRetryAttempt(attempt: RetryAttempt): Promise<void> {
    try {
      const key = `${this.REDIS_KEY_PREFIX}${attempt.clientId}:${attempt.attemptId}`;
      const ttl = Math.ceil(this.RETRY_WINDOW / 1000);
      
      await redisOperations.setex(key, ttl, JSON.stringify(attempt));
    } catch (error) {
      Logger.error('Failed to persist retry attempt:', error);
    }
  }

  /**
   * Create retry policy for specific scenarios
   */
  static createPolicy(scenario: 'auth' | 'connection' | 'heartbeat' | 'message'): RetryPolicy {
    const policies: Record<string, RetryPolicy> = {
      auth: {
        maxRetries: 3,
        baseDelay: 2000, // 2 seconds
        maxDelay: 30000, // 30 seconds
        backoffMultiplier: 2,
        jitterEnabled: true
      },
      connection: {
        maxRetries: 5,
        baseDelay: 1000, // 1 second
        maxDelay: 15000, // 15 seconds
        backoffMultiplier: 1.5,
        jitterEnabled: true
      },
      heartbeat: {
        maxRetries: 3,
        baseDelay: 5000, // 5 seconds
        maxDelay: 30000, // 30 seconds
        backoffMultiplier: 2,
        jitterEnabled: false
      },
      message: {
        maxRetries: 2,
        baseDelay: 500, // 500ms
        maxDelay: 5000, // 5 seconds
        backoffMultiplier: 2,
        jitterEnabled: true
      }
    };

    return policies[scenario] || policies.connection;
  }
}

// Export singleton instance
export const sseRetryManager = new SSERetryManager();

// Setup periodic cleanup
setInterval(() => {
  sseRetryManager.cleanup();
}, 5 * 60 * 1000); // Every 5 minutes
