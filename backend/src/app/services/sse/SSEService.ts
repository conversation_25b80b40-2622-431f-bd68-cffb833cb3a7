import { Request, Response } from 'express';
import { Logger } from '../../config/logger';

export interface SSEMessage {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  targetUserId?: string;
  room?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  expiresAt?: Date;
}

export interface SSEConnection {
  id: string;
  userId: string;
  userType: string;
  response: Response;
  request: Request;
  connectedAt: Date;
  lastActivity: Date;
  rooms: Set<string>;
  isActive: boolean;
  heartbeatInterval?: NodeJS.Timeout;
}

export interface SSEStats {
  totalConnections: number;
  activeConnections: number;
  messagesSent: number;
  messagesLast24h: number;
  averageConnectionDuration: number;
  errorRate: number;
  roomsActive: number;
}

class SSEService {
  private connections: Map<string, SSEConnection> = new Map();
  private userConnections: Map<string, Set<string>> = new Map(); // userId -> connectionIds
  private roomConnections: Map<string, Set<string>> = new Map(); // roomName -> connectionIds
  private cleanupInterval: NodeJS.Timeout | null = null;
  private stats: SSEStats = {
    totalConnections: 0,
    activeConnections: 0,
    messagesSent: 0,
    messagesLast24h: 0,
    averageConnectionDuration: 0,
    errorRate: 0,
    roomsActive: 0
  };

  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private readonly CONNECTION_TIMEOUT = 300000; // 5 minutes
  private readonly MAX_CONNECTIONS_PER_USER = 5;

  constructor() {
    this.startCleanup();
    Logger.info('📡 SSE Service initialized');
  }

  /**
   * Create a new SSE connection
   */
  public createConnection(
    req: Request,
    res: Response,
    userId: string,
    userType: string
  ): string {
    const connectionId = `sse_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Check connection limits
    const userConnectionIds = this.userConnections.get(userId) || new Set();
    if (userConnectionIds.size >= this.MAX_CONNECTIONS_PER_USER) {
      // Close oldest connection
      const oldestConnectionId = Array.from(userConnectionIds)[0];
      this.closeConnection(oldestConnectionId, 'Connection limit exceeded');
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no' // Disable nginx buffering
    });

    // Create connection object
    const connection: SSEConnection = {
      id: connectionId,
      userId,
      userType,
      response: res,
      request: req,
      connectedAt: new Date(),
      lastActivity: new Date(),
      rooms: new Set(),
      isActive: true
    };

    // Store connection
    this.connections.set(connectionId, connection);

    // Update user connections mapping
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(connectionId);

    // Start heartbeat
    this.startHeartbeat(connection);

    // Handle connection close
    req.on('close', () => {
      this.closeConnection(connectionId, 'Client disconnected');
    });

    req.on('error', (error) => {
      Logger.error(`SSE connection error for ${connectionId}:`, error);
      this.closeConnection(connectionId, 'Connection error');
    });

    // Send initial connection message
    this.sendToConnection(connectionId, {
      id: `welcome_${Date.now()}`,
      type: 'connection_established',
      data: {
        connectionId,
        serverTime: new Date(),
        heartbeatInterval: this.HEARTBEAT_INTERVAL
      },
      timestamp: new Date(),
      priority: 'medium'
    });

    // Update stats
    this.stats.totalConnections++;
    this.stats.activeConnections++;

    Logger.info(`📡 SSE connection created: ${connectionId} for user ${userId} (${userType})`);
    return connectionId;
  }

  /**
   * Close a connection
   */
  public closeConnection(connectionId: string, reason: string = 'Unknown'): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    try {
      // Clear heartbeat
      if (connection.heartbeatInterval) {
        clearInterval(connection.heartbeatInterval);
      }

      // Send close message
      if (connection.isActive) {
        this.sendToConnection(connectionId, {
          id: `close_${Date.now()}`,
          type: 'connection_closing',
          data: { reason },
          timestamp: new Date(),
          priority: 'high'
        });
      }

      // Close response
      if (!connection.response.destroyed) {
        connection.response.end();
      }

      // Remove from mappings
      this.connections.delete(connectionId);

      const userConnectionIds = this.userConnections.get(connection.userId);
      if (userConnectionIds) {
        userConnectionIds.delete(connectionId);
        if (userConnectionIds.size === 0) {
          this.userConnections.delete(connection.userId);
        }
      }

      // Remove from rooms
      connection.rooms.forEach(roomName => {
        const roomConnectionIds = this.roomConnections.get(roomName);
        if (roomConnectionIds) {
          roomConnectionIds.delete(connectionId);
          if (roomConnectionIds.size === 0) {
            this.roomConnections.delete(roomName);
          }
        }
      });

      // Update stats
      this.stats.activeConnections--;

      Logger.info(`📡 SSE connection closed: ${connectionId} (${reason})`);
    } catch (error) {
      Logger.error(`Error closing SSE connection ${connectionId}:`, error);
    }
  }

  /**
   * Send message to specific connection
   */
  private sendToConnection(connectionId: string, message: SSEMessage): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isActive || connection.response.destroyed) {
      return false;
    }

    try {
      const sseData = `id: ${message.id}\nevent: ${message.type}\ndata: ${JSON.stringify({
        ...message.data,
        timestamp: message.timestamp,
        priority: message.priority
      })}\n\n`;

      connection.response.write(sseData);
      connection.lastActivity = new Date();

      this.stats.messagesSent++;
      this.stats.messagesLast24h++;

      return true;
    } catch (error) {
      Logger.error(`Failed to send SSE message to ${connectionId}:`, error);
      this.closeConnection(connectionId, 'Send error');
      return false;
    }
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, message: SSEMessage): number {
    const userConnectionIds = this.userConnections.get(userId);
    if (!userConnectionIds || userConnectionIds.size === 0) {
      return 0;
    }

    let sentCount = 0;
    userConnectionIds.forEach(connectionId => {
      if (this.sendToConnection(connectionId, message)) {
        sentCount++;
      }
    });

    Logger.debug(`📡 Sent SSE message to user ${userId}: ${sentCount} connections`);
    return sentCount;
  }

  /**
   * Send message to specific room
   */
  public sendToRoom(roomName: string, message: SSEMessage): number {
    const roomConnectionIds = this.roomConnections.get(roomName);
    if (!roomConnectionIds || roomConnectionIds.size === 0) {
      return 0;
    }

    let sentCount = 0;
    roomConnectionIds.forEach(connectionId => {
      if (this.sendToConnection(connectionId, message)) {
        sentCount++;
      }
    });

    Logger.debug(`📡 Sent SSE message to room ${roomName}: ${sentCount} connections`);
    return sentCount;
  }

  /**
   * Broadcast message to all connections
   */
  public broadcast(message: SSEMessage): number {
    let sentCount = 0;
    this.connections.forEach((_connection, connectionId) => {
      if (this.sendToConnection(connectionId, message)) {
        sentCount++;
      }
    });

    Logger.debug(`📡 Broadcast SSE message: ${sentCount} connections`);
    return sentCount;
  }

  /**
   * Send message to all users of specific type
   */
  public sendToUserType(userType: string, message: SSEMessage): number {
    let sentCount = 0;
    this.connections.forEach((connection, connectionId) => {
      if (connection.userType === userType) {
        if (this.sendToConnection(connectionId, message)) {
          sentCount++;
        }
      }
    });

    Logger.debug(`📡 Sent SSE message to user type ${userType}: ${sentCount} connections`);
    return sentCount;
  }

  /**
   * Join a room
   */
  public joinRoom(connectionId: string, roomName: string): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection || !connection.isActive) {
      return false;
    }

    // Add to room
    if (!this.roomConnections.has(roomName)) {
      this.roomConnections.set(roomName, new Set());
    }
    this.roomConnections.get(roomName)!.add(connectionId);
    connection.rooms.add(roomName);

    // Update stats
    this.stats.roomsActive = this.roomConnections.size;

    Logger.debug(`📡 Connection ${connectionId} joined room ${roomName}`);
    return true;
  }

  /**
   * Leave a room
   */
  public leaveRoom(connectionId: string, roomName: string): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return false;
    }

    // Remove from room
    const roomConnectionIds = this.roomConnections.get(roomName);
    if (roomConnectionIds) {
      roomConnectionIds.delete(connectionId);
      if (roomConnectionIds.size === 0) {
        this.roomConnections.delete(roomName);
      }
    }
    connection.rooms.delete(roomName);

    // Update stats
    this.stats.roomsActive = this.roomConnections.size;

    Logger.debug(`📡 Connection ${connectionId} left room ${roomName}`);
    return true;
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): SSEStats {
    return { ...this.stats };
  }

  /**
   * Get connected users
   */
  public getConnectedUsers(): Array<{ userId: string; userType: string; connectionCount: number; rooms: string[] }> {
    const users: Array<{ userId: string; userType: string; connectionCount: number; rooms: string[] }> = [];
    const userMap = new Map<string, { userType: string; connectionCount: number; rooms: Set<string> }>();

    this.connections.forEach(connection => {
      if (!userMap.has(connection.userId)) {
        userMap.set(connection.userId, {
          userType: connection.userType,
          connectionCount: 0,
          rooms: new Set()
        });
      }

      const userData = userMap.get(connection.userId)!;
      userData.connectionCount++;
      connection.rooms.forEach(room => userData.rooms.add(room));
    });

    userMap.forEach((userData, userId) => {
      users.push({
        userId,
        userType: userData.userType,
        connectionCount: userData.connectionCount,
        rooms: Array.from(userData.rooms)
      });
    });

    return users;
  }

  /**
   * Start heartbeat for connection
   */
  private startHeartbeat(connection: SSEConnection): void {
    connection.heartbeatInterval = setInterval(() => {
      if (!connection.isActive || connection.response.destroyed) {
        this.closeConnection(connection.id, 'Heartbeat failed');
        return;
      }

      try {
        const heartbeatMessage: SSEMessage = {
          id: `heartbeat_${Date.now()}`,
          type: 'heartbeat',
          data: { timestamp: new Date() },
          timestamp: new Date(),
          priority: 'low'
        };

        this.sendToConnection(connection.id, heartbeatMessage);
      } catch (error) {
        Logger.error(`Heartbeat failed for connection ${connection.id}:`, error);
        this.closeConnection(connection.id, 'Heartbeat error');
      }
    }, this.HEARTBEAT_INTERVAL);
  }

  /**
   * Start cleanup process
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleConnections();
      this.resetDailyStats();
    }, 60000); // Run every minute
  }

  /**
   * Clean up stale connections
   */
  private cleanupStaleConnections(): void {
    const now = Date.now();
    const staleConnections: string[] = [];

    this.connections.forEach((connection, connectionId) => {
      const timeSinceLastActivity = now - connection.lastActivity.getTime();

      if (timeSinceLastActivity > this.CONNECTION_TIMEOUT || connection.response.destroyed) {
        staleConnections.push(connectionId);
      }
    });

    staleConnections.forEach(connectionId => {
      this.closeConnection(connectionId, 'Stale connection cleanup');
    });

    if (staleConnections.length > 0) {
      Logger.info(`📡 Cleaned up ${staleConnections.length} stale SSE connections`);
    }
  }

  /**
   * Reset daily statistics
   */
  private resetDailyStats(): void {
    const now = new Date();
    const isNewDay = now.getHours() === 0 && now.getMinutes() === 0;

    if (isNewDay) {
      this.stats.messagesLast24h = 0;
      Logger.info('📡 Reset daily SSE statistics');
    }
  }

  /**
   * Shutdown service
   */
  public shutdown(): void {
    Logger.info('📡 Shutting down SSE Service...');

    // Close all connections
    this.connections.forEach((_connection, connectionId) => {
      this.closeConnection(connectionId, 'Service shutdown');
    });

    // Clear intervals
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Clear maps
    this.connections.clear();
    this.userConnections.clear();
    this.roomConnections.clear();

    Logger.info('📡 SSE Service shutdown complete');
  }
}

export default SSEService;