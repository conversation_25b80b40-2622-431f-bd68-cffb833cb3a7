import { useCallback, useState, useEffect, useRef } from 'react';
import { useRealTimeAnalytics } from './useRealTimeAnalytics';
import { useCourseOptimisticUpdates } from './useCourseOptimisticUpdates';
import { useAppDispatch } from '@/redux/hooks';
import { baseApi } from '@/redux/api/baseApi';

import { ICourse } from '@/types/course';
import { ILecture } from '@/redux/features/lecture/lectureSlice';
import { toast } from '@/utils/toast';

interface DashboardState {
  courses: ICourse[];
  lectures: { [courseId: string]: ILecture[] };
  stats: {
    totalCourses: number;
    totalLectures: number;
    totalStudents: number;
    totalEarnings: number;
    lastUpdated: Date;
  };
  isLoading: boolean;
  error: string | null;
}

interface UseRealTimeDashboardOptions {
  enableOptimisticUpdates?: boolean;
  enableRealTimeSync?: boolean;
  pollingInterval?: number;
}

export const useRealTimeDashboard = (options: UseRealTimeDashboardOptions = {}) => {
  const {
    enableOptimisticUpdates = true,
    enableRealTimeSync = true,
    pollingInterval = 30000
  } = options;

  const dispatch = useAppDispatch();
  const isUnmountingRef = useRef(false);

  const [dashboardState, setDashboardState] = useState<DashboardState>({
    courses: [],
    lectures: {},
    stats: {
      totalCourses: 0,
      totalLectures: 0,
      totalStudents: 0,
      totalEarnings: 0,
      lastUpdated: new Date()
    },
    isLoading: false,
    error: null
  });

  const stateRef = useRef(dashboardState);
  stateRef.current = dashboardState;

  // Individual update handlers (defined first to avoid temporal dead zone)
  const handleCourseUpdate = useCallback((data: Record<string, unknown>) => {
    if (isUnmountingRef.current) return;

    const { action, course, courses } = data;

    setDashboardState(prev => {
      if (action === 'create' && course) {
        return {
          ...prev,
          courses: [...prev.courses, course as any],
          stats: {
            ...prev.stats,
            totalCourses: prev.stats.totalCourses + 1,
            lastUpdated: new Date()
          }
        };
      } else if (action === 'update' && course) {
        return {
          ...prev,
          courses: prev.courses.map(c =>
            c._id === (course as any)._id ? { ...c, ...(course as any) } : c
          ),
          stats: {
            ...prev.stats,
            lastUpdated: new Date()
          }
        };
      } else if (action === 'delete' && course) {
        return {
          ...prev,
          courses: prev.courses.filter(c => c._id !== (course as any)._id),
          stats: {
            ...prev.stats,
            totalCourses: Math.max(0, prev.stats.totalCourses - 1),
            lastUpdated: new Date()
          }
        };
      } else if (action === 'bulk' && courses) {
        return {
          ...prev,
          courses: courses as any[],
          stats: {
            ...prev.stats,
            totalCourses: (courses as any[]).length,
            lastUpdated: new Date()
          }
        };
      }
      return prev;
    });
  }, []);

  const handleLectureUpdate = useCallback((data: any) => {
    const { action, lecture, lectures, courseId } = data;

    setDashboardState(prev => {
      const updatedLectures = { ...prev.lectures };

      if (action === 'create' && lecture && courseId) {
        const existingLectures = updatedLectures[courseId] || [];
        updatedLectures[courseId] = [...existingLectures, lecture];
      } else if (action === 'update' && lecture && courseId) {
        const existingLectures = updatedLectures[courseId] || [];
        updatedLectures[courseId] = existingLectures.map(l =>
          l._id === lecture._id ? { ...l, ...lecture } : l
        );
      } else if (action === 'delete' && lecture && courseId) {
        const existingLectures = updatedLectures[courseId] || [];
        updatedLectures[courseId] = existingLectures.filter(l => l._id !== lecture._id);
      } else if (action === 'bulk' && lectures && courseId) {
        updatedLectures[courseId] = lectures;
      }

      return {
        ...prev,
        lectures: updatedLectures,
        stats: {
          ...prev.stats,
          totalLectures: Object.values(updatedLectures).reduce((total, courseLectures) =>
            total + courseLectures.length, 0
          ),
          lastUpdated: new Date()
        }
      };
    });
  }, []);

  const handleEnrollmentUpdate = useCallback((data: any) => {
    const { courseId, studentCount, totalStudents } = data;

    setDashboardState(prev => {
      const updatedCourses = prev.courses.map(course =>
        course._id === courseId
          ? { ...course, enrolledStudents: studentCount }
          : course
      );

      return {
        ...prev,
        courses: updatedCourses,
        stats: {
          ...prev.stats,
          totalStudents: totalStudents || prev.stats.totalStudents,
          lastUpdated: new Date()
        }
      };
    });

    toast.success('New enrollment!', {
      description: 'A student has enrolled in one of your courses.'
    });
  }, []);

  const handleRevenueUpdate = useCallback((data: any) => {
    const { amount, totalEarnings } = data;

    setDashboardState(prev => ({
      ...prev,
      stats: {
        ...prev.stats,
        totalEarnings: totalEarnings || prev.stats.totalEarnings + amount,
        lastUpdated: new Date()
      }
    }));

    // Immediate toast notification with better styling
    toast.realTime.payment(amount);
  }, []);

  const handleAnalyticsUpdate = useCallback((data: any) => {
    setDashboardState(prev => ({
      ...prev,
      stats: {
        ...prev.stats,
        ...data,
        lastUpdated: new Date()
      }
    }));
  }, []);

  // Enhanced cache invalidation for real-time updates
  const invalidateRelatedCaches = useCallback((type: string, data: Record<string, unknown>) => {
    if (isUnmountingRef.current) return;

    // Invalidate relevant RTK Query caches based on update type
    switch (type) {
      case 'course-update':
        dispatch(baseApi.util.invalidateTags(['courses', 'course']));
        break;
      case 'lecture-update':
        dispatch(baseApi.util.invalidateTags(['lectures', 'course']));
        if (data.courseId) {
          dispatch(baseApi.util.invalidateTags([{ type: 'lectures', id: data.courseId as string | number }]));
        }
        break;
      case 'enrollment':
        dispatch(baseApi.util.invalidateTags(['courses', 'analytics']));
        break;
      case 'revenue':
        dispatch(baseApi.util.invalidateTags(['analytics', 'UpcomingPayout']));
        break;
      default:
        break;
    }
  }, [dispatch]);

  // Handle real-time updates from WebSocket with improved cache invalidation
  const handleRealTimeUpdate = useCallback((update: { type: string; data: Record<string, unknown> }) => {
    if (isUnmountingRef.current) return;

    const { type, data } = update;

    // Invalidate caches first for immediate UI updates
    invalidateRelatedCaches(type, data);

    switch (type) {
      case 'course-update':
        handleCourseUpdate(data);
        break;
      case 'lecture-update':
        handleLectureUpdate(data);
        break;
      case 'enrollment':
        handleEnrollmentUpdate(data);
        break;
      case 'revenue':
        handleRevenueUpdate(data);
        break;
      case 'analytics':
        handleAnalyticsUpdate(data);
        break;
    }
  }, [invalidateRelatedCaches, handleCourseUpdate, handleLectureUpdate, handleEnrollmentUpdate, handleRevenueUpdate, handleAnalyticsUpdate]);

  // Real-time analytics connection with enhanced status tracking
  const {
    isConnected,
    connectionState,
    connectionError,
    lastUpdate: realTimeLastUpdate,
    reconnectAttempts,
    maxReconnectAttempts,
    reconnect
  } = useRealTimeAnalytics({
    enableWebSocket: enableRealTimeSync,
    pollingInterval,
    onUpdate: handleRealTimeUpdate
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountingRef.current = true;
    };
  }, []);

  // Course optimistic updates
  const courseOptimistic = useCourseOptimisticUpdates({
    courses: dashboardState.courses,
    onCoursesChange: (courses) => {
      setDashboardState(prev => ({
        ...prev,
        courses,
        stats: {
          ...prev.stats,
          totalCourses: courses.length,
          lastUpdated: new Date()
        }
      }));
    }
  });


  // Initialize dashboard data
  const initializeDashboard = useCallback((initialData: Partial<DashboardState>) => {
    setDashboardState(prev => ({
      ...prev,
      ...initialData,
      stats: {
        ...prev.stats,
        ...initialData.stats,
        lastUpdated: new Date()
      }
    }));
  }, []);

  // Manual refresh function with immediate feedback
  const refreshDashboard = useCallback(async () => {
    setDashboardState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // This would trigger refetch of all dashboard data
      // The actual implementation depends on your API structure

      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        stats: {
          ...prev.stats,
          lastUpdated: new Date()
        }
      }));

      // Immediate success feedback
      toast.success('Dashboard refreshed!', {
        duration: 2000,
        position: "top-right"
      });
    } catch (error) {
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to refresh dashboard'
      }));

      // Immediate error feedback
      toast.error('Failed to refresh dashboard', {
        duration: 4000,
        position: "top-right"
      });
    }
  }, []);

  // Lecture updates will be handled through the main real-time connection
  const updateLectureStats = useCallback((courseId: string, lectures: any[]) => {
    setDashboardState(prev => ({
      ...prev,
      lectures: {
        ...prev.lectures,
        [courseId]: lectures
      },
      stats: {
        ...prev.stats,
        totalLectures: Object.values({
          ...prev.lectures,
          [courseId]: lectures
        }).reduce((total, courseLectures) => total + courseLectures.length, 0),
        lastUpdated: new Date()
      }
    }));
  }, []);

  return {
    // State
    dashboardState,
    isRealTimeConnected: isConnected,
    
    // Actions
    initializeDashboard,
    refreshDashboard,
    
    // Optimistic updates
    courseOptimistic: enableOptimisticUpdates ? courseOptimistic : null,
    updateLectureStats: enableOptimisticUpdates ? updateLectureStats : null,
    
    // Enhanced real-time status with enterprise-level monitoring
    connectionStatus: {
      isConnected,
      connectionState,
      connectionError,
      lastUpdate: realTimeLastUpdate || dashboardState.stats.lastUpdated,
      hasError: !!dashboardState.error || !!connectionError,
      reconnectAttempts,
      maxReconnectAttempts,
      reconnect
    }
  };
};
