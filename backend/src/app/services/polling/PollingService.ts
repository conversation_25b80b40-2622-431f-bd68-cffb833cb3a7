import { Logger } from '../../config/logger';
import { redisOperations } from '../../config/redis';
import { resourceManagerService } from '../resource/ResourceManagerService';

export interface PollingUpdate {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  userId?: string;
  userType?: 'user' | 'teacher' | 'student';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  expiresAt?: Date;
  batchId?: string;
}

export interface PollingSubscription {
  id: string;
  userId: string;
  userType: 'user' | 'teacher' | 'student';
  types: string[];
  lastPolled: Date;
  isActive: boolean;
  failureCount: number;
  nextPollTime: Date;
  backoffMultiplier: number;
  resourceId?: string;
}

export interface PollingBatch {
  id: string;
  updates: PollingUpdate[];
  timestamp: Date;
  totalUpdates: number;
  hasMore: boolean;
}

export interface PollingStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalUpdates: number;
  updatesLast24h: number;
  averagePollingInterval: number;
  errorRate: number;
  batchesProcessed: number;
}

class PollingService {
  private subscriptions: Map<string, PollingSubscription> = new Map();
  private updates: Map<string, PollingUpdate[]> = new Map(); // userId -> updates
  private globalUpdates: PollingUpdate[] = []; // For broadcast updates
  private cleanupInterval: NodeJS.Timeout | null = null;
  private stats: PollingStats = {
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    totalUpdates: 0,
    updatesLast24h: 0,
    averagePollingInterval: 0,
    errorRate: 0,
    batchesProcessed: 0
  };

  private readonly MIN_POLLING_INTERVAL = 1000; // 1 second
  private readonly MAX_POLLING_INTERVAL = 30000; // 30 seconds
  private readonly DEFAULT_POLLING_INTERVAL = 5000; // 5 seconds
  private readonly BACKOFF_MULTIPLIER = 1.5;
  private readonly MAX_FAILURE_COUNT = 5;
  private readonly MAX_UPDATES_PER_BATCH = 50;
  private readonly UPDATE_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    this.startCleanup();
    Logger.info('📊 Polling Service initialized');
  }

  /**
   * Create or update polling subscription
   */
  public subscribe(
    userId: string,
    userType: 'user' | 'teacher' | 'student',
    types: string[] = ['*']
  ): string {
    const subscriptionId = `poll_${userId}_${Date.now()}`;
    
    const subscription: PollingSubscription = {
      id: subscriptionId,
      userId,
      userType,
      types,
      lastPolled: new Date(),
      isActive: true,
      failureCount: 0,
      nextPollTime: new Date(Date.now() + this.DEFAULT_POLLING_INTERVAL),
      backoffMultiplier: 1
    };

    this.subscriptions.set(subscriptionId, subscription);
    this.stats.totalSubscriptions++;
    this.stats.activeSubscriptions++;

    // Register with resource manager
    const resourceId = resourceManagerService.registerResource({
      type: 'polling_subscription',
      metadata: {
        subscriptionId,
        userId,
        userType,
        types
      },
      cleanup: async () => {
        this.unsubscribe(subscriptionId);
      },
      priority: 'medium'
    });

    // Store resource ID in subscription
    subscription.resourceId = resourceId;

    Logger.info(`📊 Polling subscription created: ${subscriptionId} for user ${userId} (${userType})`);
    return subscriptionId;
  }

  /**
   * Remove polling subscription
   */
  public unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    subscription.isActive = false;
    this.subscriptions.delete(subscriptionId);
    this.stats.activeSubscriptions = Math.max(0, this.stats.activeSubscriptions - 1);

    Logger.info(`📊 Polling subscription removed: ${subscriptionId}`);
    return true;
  }

  /**
   * Add update for specific user
   */
  public addUpdate(update: PollingUpdate): void {
    // Add to user-specific updates
    if (update.userId) {
      if (!this.updates.has(update.userId)) {
        this.updates.set(update.userId, []);
      }
      
      const userUpdates = this.updates.get(update.userId)!;
      userUpdates.push(update);
      
      // Limit update queue size
      if (userUpdates.length > this.MAX_UPDATES_PER_BATCH * 2) {
        userUpdates.splice(0, userUpdates.length - this.MAX_UPDATES_PER_BATCH * 2);
      }
    } else {
      // Add to global updates for broadcast
      this.globalUpdates.push(update);
      
      // Limit global update queue size
      if (this.globalUpdates.length > this.MAX_UPDATES_PER_BATCH * 2) {
        this.globalUpdates.splice(0, this.globalUpdates.length - this.MAX_UPDATES_PER_BATCH * 2);
      }
    }

    this.stats.totalUpdates++;
    this.stats.updatesLast24h++;

    Logger.debug(`📊 Update added: ${update.type} for ${update.userId || 'broadcast'}`);
  }

  /**
   * Add multiple updates in batch
   */
  public addBatchUpdates(updates: PollingUpdate[]): void {
    for (const update of updates) {
      this.addUpdate(update);
    }
    
    Logger.info(`📊 Batch updates added: ${updates.length} updates`);
  }

  /**
   * Poll for updates
   */
  public poll(subscriptionId: string, lastUpdateId?: string): PollingBatch | null {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription || !subscription.isActive) {
      return null;
    }

    try {
      // Update last polled time
      subscription.lastPolled = new Date();
      subscription.failureCount = 0;
      subscription.backoffMultiplier = 1;
      subscription.nextPollTime = new Date(Date.now() + this.calculatePollingInterval(subscription));

      // Get user-specific updates
      const userUpdates = this.updates.get(subscription.userId) || [];
      
      // Get relevant global updates
      const relevantGlobalUpdates = this.globalUpdates.filter(update => 
        this.isUpdateRelevant(update, subscription)
      );

      // Combine and sort updates
      const allUpdates = [...userUpdates, ...relevantGlobalUpdates]
        .filter(update => this.isUpdateValid(update))
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      // Filter updates after lastUpdateId if provided
      let filteredUpdates = allUpdates;
      if (lastUpdateId) {
        const lastIndex = allUpdates.findIndex(update => update.id === lastUpdateId);
        if (lastIndex >= 0) {
          filteredUpdates = allUpdates.slice(lastIndex + 1);
        }
      }

      // Limit batch size
      const batchUpdates = filteredUpdates.slice(0, this.MAX_UPDATES_PER_BATCH);
      const hasMore = filteredUpdates.length > this.MAX_UPDATES_PER_BATCH;

      const batch: PollingBatch = {
        id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        updates: batchUpdates,
        timestamp: new Date(),
        totalUpdates: batchUpdates.length,
        hasMore
      };

      this.stats.batchesProcessed++;

      Logger.debug(`📊 Poll completed: ${subscriptionId} - ${batchUpdates.length} updates`);
      return batch;
    } catch (error) {
      Logger.error(`Polling failed for subscription ${subscriptionId}:`, error);
      this.handlePollingFailure(subscription);
      return null;
    }
  }

  /**
   * Get next polling time for subscription
   */
  public getNextPollTime(subscriptionId: string): Date | null {
    const subscription = this.subscriptions.get(subscriptionId);
    return subscription ? subscription.nextPollTime : null;
  }

  /**
   * Get polling statistics
   */
  public getStats(): PollingStats {
    return { ...this.stats };
  }

  /**
   * Get active subscriptions
   */
  public getActiveSubscriptions(): PollingSubscription[] {
    return Array.from(this.subscriptions.values()).filter(sub => sub.isActive);
  }

  /**
   * Clear updates for user
   */
  public clearUpdatesForUser(userId: string): void {
    this.updates.delete(userId);
    Logger.info(`📊 Updates cleared for user: ${userId}`);
  }

  /**
   * Clear all global updates
   */
  public clearGlobalUpdates(): void {
    this.globalUpdates.length = 0;
    Logger.info('📊 Global updates cleared');
  }

  /**
   * Private helper methods
   */
  private calculatePollingInterval(subscription: PollingSubscription): number {
    const baseInterval = this.DEFAULT_POLLING_INTERVAL;
    const backoffInterval = baseInterval * Math.pow(this.BACKOFF_MULTIPLIER, subscription.backoffMultiplier);
    
    return Math.min(Math.max(backoffInterval, this.MIN_POLLING_INTERVAL), this.MAX_POLLING_INTERVAL);
  }

  private handlePollingFailure(subscription: PollingSubscription): void {
    subscription.failureCount++;
    subscription.backoffMultiplier = Math.min(subscription.backoffMultiplier + 1, 5);
    subscription.nextPollTime = new Date(Date.now() + this.calculatePollingInterval(subscription));

    if (subscription.failureCount >= this.MAX_FAILURE_COUNT) {
      subscription.isActive = false;
      this.stats.activeSubscriptions = Math.max(0, this.stats.activeSubscriptions - 1);
      Logger.warn(`Polling subscription deactivated due to failures: ${subscription.id}`);
    }

    this.stats.errorRate = (this.stats.errorRate + 1) / 2; // Simple moving average
  }

  private isUpdateRelevant(update: PollingUpdate, subscription: PollingSubscription): boolean {
    // Check if update type is subscribed
    if (!subscription.types.includes('*') && !subscription.types.includes(update.type)) {
      return false;
    }

    // Check user type relevance
    if (update.userType && update.userType !== subscription.userType) {
      return false;
    }

    // Check if update is not for specific user (global update)
    return !update.userId;
  }

  private isUpdateValid(update: PollingUpdate): boolean {
    const now = new Date();
    
    // Check if update has expired
    if (update.expiresAt && update.expiresAt < now) {
      return false;
    }

    // Check if update is too old
    const maxAge = now.getTime() - this.UPDATE_RETENTION_TIME;
    if (update.timestamp.getTime() < maxAge) {
      return false;
    }

    return true;
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 60000); // Run cleanup every minute
  }

  private performCleanup(): void {
    const now = new Date();
    const maxAge = now.getTime() - this.UPDATE_RETENTION_TIME;

    // Clean up expired updates
    for (const [userId, updates] of this.updates.entries()) {
      const validUpdates = updates.filter(update => 
        update.timestamp.getTime() > maxAge && 
        (!update.expiresAt || update.expiresAt > now)
      );
      
      if (validUpdates.length === 0) {
        this.updates.delete(userId);
      } else if (validUpdates.length !== updates.length) {
        this.updates.set(userId, validUpdates);
      }
    }

    // Clean up expired global updates
    this.globalUpdates = this.globalUpdates.filter(update => 
      update.timestamp.getTime() > maxAge && 
      (!update.expiresAt || update.expiresAt > now)
    );

    // Clean up inactive subscriptions
    for (const [id, subscription] of this.subscriptions.entries()) {
      if (!subscription.isActive || 
          (now.getTime() - subscription.lastPolled.getTime()) > this.UPDATE_RETENTION_TIME) {
        this.subscriptions.delete(id);
        this.stats.activeSubscriptions = Math.max(0, this.stats.activeSubscriptions - 1);
      }
    }

    // Reset daily stats if needed
    const hoursAgo24 = now.getTime() - (24 * 60 * 60 * 1000);
    if (this.stats.updatesLast24h > 0) {
      // This is a simplified reset - in production, you'd want more sophisticated time-based tracking
      this.stats.updatesLast24h = Math.floor(this.stats.updatesLast24h * 0.95);
    }

    Logger.debug('📊 Polling service cleanup completed');
  }

  /**
   * Broadcast update to all users of a type
   */
  public broadcastToUserType(userType: 'user' | 'teacher' | 'student', update: Omit<PollingUpdate, 'id' | 'timestamp'>): void {
    const fullUpdate: PollingUpdate = {
      ...update,
      id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      userType
    };

    this.addUpdate(fullUpdate);
  }

  /**
   * Broadcast update to specific room/course
   */
  public broadcastToRoom(roomId: string, update: Omit<PollingUpdate, 'id' | 'timestamp'>): void {
    const fullUpdate: PollingUpdate = {
      ...update,
      id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      data: {
        ...update.data,
        roomId
      }
    };

    this.addUpdate(fullUpdate);
  }

  /**
   * Shutdown polling service
   */
  public shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.subscriptions.clear();
    this.updates.clear();
    this.globalUpdates.length = 0;

    Logger.info('📊 Polling Service shutdown complete');
  }
}

export default PollingService;
