import { useState, useEffect, useCallback, useRef } from 'react';
import { useRealTime } from './useRealTime';
import { toast } from 'sonner';

export interface AnalyticsUpdate {
  type: 'enrollment' | 'revenue' | 'performance' | 'engagement' | 'dashboard' | 'course_update' | 'lecture_update' | 'analytics_batch';
  data: any;
  timestamp: string;
  teacherId?: string;
  courseId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface AnalyticsConnectionState {
  isConnected: boolean;
  connectionMethod: 'sse' | 'polling' | 'both' | 'none';
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  lastUpdate: AnalyticsUpdate | null;
  updatesReceived: number;
  connectionError: string | null;
}

export interface UseRealTimeAnalyticsOptions {
  enableWebSocket?: boolean; // Kept for compatibility, but ignored
  pollingInterval?: number;
  onUpdate?: (update: AnalyticsUpdate) => void;
  onConnectionChange?: (state: AnalyticsConnectionState) => void;
  autoConnect?: boolean;
}

export const useRealTimeAnalytics = (options: UseRealTimeAnalyticsOptions = {}) => {
  const {
    pollingInterval = 5000,
    onUpdate,
    onConnectionChange,
    autoConnect = true
  } = options;

  const [connectionState, setConnectionState] = useState<AnalyticsConnectionState>({
    isConnected: false,
    connectionMethod: 'none',
    connectionQuality: 'offline',
    lastUpdate: null,
    updatesReceived: 0,
    connectionError: null
  });

  const updateCallbackRef = useRef(onUpdate);
  const connectionCallbackRef = useRef(onConnectionChange);

  // Update refs when callbacks change
  useEffect(() => {
    updateCallbackRef.current = onUpdate;
  }, [onUpdate]);

  useEffect(() => {
    connectionCallbackRef.current = onConnectionChange;
  }, [onConnectionChange]);

  // Real-time connection with SSE and polling fallback
  const realTime = useRealTime({
    preferSSE: true,
    enablePollingFallback: true,
    pollingTypes: ['analytics_update', 'analytics_batch', 'enrollment', 'revenue', 'performance', 'engagement', 'dashboard', 'course_update', 'lecture_update'],
    autoConnect,
    onUpdate: (update) => {
      // Filter analytics-related updates
      if (isAnalyticsUpdate(update)) {
        const analyticsUpdate: AnalyticsUpdate = {
          type: update.type as AnalyticsUpdate['type'],
          data: update.data,
          timestamp: update.timestamp,
          teacherId: update.data.teacherId,
          courseId: update.data.courseId,
          priority: update.priority
        };

        setConnectionState(prev => ({
          ...prev,
          lastUpdate: analyticsUpdate,
          updatesReceived: prev.updatesReceived + 1
        }));

        updateCallbackRef.current?.(analyticsUpdate);
        handleAnalyticsUpdate(analyticsUpdate);
      }
    },
    onConnectionChange: (stats) => {
      const newState: AnalyticsConnectionState = {
        isConnected: stats.isConnected,
        connectionMethod: stats.connectionMethod,
        connectionQuality: stats.connectionQuality,
        lastUpdate: connectionState.lastUpdate,
        updatesReceived: connectionState.updatesReceived,
        connectionError: stats.sseStatus === 'error' || stats.pollingStatus === 'error' ? 'Connection error' : null
      };

      setConnectionState(newState);
      connectionCallbackRef.current?.(newState);
    }
  });

  /**
   * Check if update is analytics-related
   */
  const isAnalyticsUpdate = (update: any): boolean => {
    const analyticsTypes = [
      'analytics_update', 'analytics_batch', 'enrollment', 'revenue', 
      'performance', 'engagement', 'dashboard', 'course_update', 'lecture_update'
    ];
    return analyticsTypes.includes(update.type);
  };

  /**
   * Handle specific analytics update types
   */
  const handleAnalyticsUpdate = useCallback((update: AnalyticsUpdate) => {
    switch (update.type) {
      case 'enrollment':
        handleEnrollmentUpdate(update);
        break;
      case 'revenue':
        handleRevenueUpdate(update);
        break;
      case 'performance':
        handlePerformanceUpdate(update);
        break;
      case 'engagement':
        handleEngagementUpdate(update);
        break;
      case 'dashboard':
        handleDashboardUpdate(update);
        break;
      case 'course_update':
        handleCourseUpdate(update);
        break;
      case 'lecture_update':
        handleLectureUpdate(update);
        break;
      case 'analytics_batch':
        handleAnalyticsBatch(update);
        break;
      default:
        console.debug('Unhandled analytics update type:', update.type);
    }
  }, []);

  const handleEnrollmentUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for enrollment components
    window.dispatchEvent(new CustomEvent('enrollment-update', {
      detail: update.data
    }));

    // Show notification for significant enrollment changes
    if (update.priority === 'high' || update.priority === 'urgent') {
      toast.success('New Enrollment', {
        description: `You have a new student enrollment!`,
        duration: 5000,
      });
    }
  };

  const handleRevenueUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for revenue components
    window.dispatchEvent(new CustomEvent('revenue-update', {
      detail: update.data
    }));

    // Show notification for significant revenue changes
    if (update.priority === 'high' || update.priority === 'urgent') {
      toast.success('Revenue Update', {
        description: `Your earnings have been updated!`,
        duration: 5000,
      });
    }
  };

  const handlePerformanceUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for performance components
    window.dispatchEvent(new CustomEvent('performance-update', {
      detail: update.data
    }));
  };

  const handleEngagementUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for engagement components
    window.dispatchEvent(new CustomEvent('engagement-update', {
      detail: update.data
    }));
  };

  const handleDashboardUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for dashboard components
    window.dispatchEvent(new CustomEvent('dashboard-update', {
      detail: update.data
    }));
  };

  const handleCourseUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for course components
    window.dispatchEvent(new CustomEvent('course-update', {
      detail: update.data
    }));

    // Show notification for course updates
    if (update.data.action === 'updated') {
      toast.info('Course Updated', {
        description: `Course "${update.data.course?.title || 'Unknown'}" has been updated`,
        duration: 3000,
      });
    }
  };

  const handleLectureUpdate = (update: AnalyticsUpdate) => {
    // Dispatch custom event for lecture components
    window.dispatchEvent(new CustomEvent('lecture-update', {
      detail: update.data
    }));

    // Show notification for lecture updates
    if (update.data.action === 'updated') {
      toast.info('Lecture Updated', {
        description: `A lecture has been updated in your course`,
        duration: 3000,
      });
    }
  };

  const handleAnalyticsBatch = (update: AnalyticsUpdate) => {
    // Handle batch updates
    if (update.data.updates && Array.isArray(update.data.updates)) {
      for (const batchUpdate of update.data.updates) {
        handleAnalyticsUpdate(batchUpdate);
      }
    }

    // Dispatch batch event
    window.dispatchEvent(new CustomEvent('analytics-batch-update', {
      detail: update.data
    }));
  };

  /**
   * Send message (compatibility method - now uses HTTP API)
   */
  const sendMessage = useCallback((data: any) => {
    console.warn('sendMessage is deprecated. Real-time communication now uses HTTP APIs.');
    // This would need to be implemented as HTTP API calls
  }, []);

  /**
   * Manual reconnection
   */
  const reconnect = useCallback(() => {
    realTime.reconnect();
  }, [realTime]);

  /**
   * Get connection statistics
   */
  const getConnectionStats = useCallback(() => {
    return {
      isConnected: connectionState.isConnected,
      connectionMethod: connectionState.connectionMethod,
      connectionQuality: connectionState.connectionQuality,
      updatesReceived: connectionState.updatesReceived,
      lastUpdate: connectionState.lastUpdate,
      connectionError: connectionState.connectionError,
      // Additional stats from real-time hook
      sseConnected: realTime.sse.isConnected,
      pollingActive: realTime.polling.isActive,
      pollingInterval: realTime.polling.currentInterval
    };
  }, [connectionState, realTime]);

  return {
    // Connection state
    isConnected: connectionState.isConnected,
    connectionState: connectionState.connectionMethod,
    connectionError: connectionState.connectionError,
    lastUpdate: connectionState.lastUpdate,
    reconnectAttempts: 0, // Not applicable with new system
    maxReconnectAttempts: 0, // Not applicable with new system
    
    // Methods
    sendMessage,
    reconnect,
    getConnectionStats,
    
    // Additional properties for compatibility
    connectionQuality: connectionState.connectionQuality,
    updatesReceived: connectionState.updatesReceived,
    
    // New real-time system properties
    realTimeStats: realTime,
    isSSEConnected: realTime.sse.isConnected,
    isPollingActive: realTime.polling.isActive
  };
};
