import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';
import { Express } from 'express';
import { createTestApp } from './helpers/testApp';
import { createTestUser, createTestTeacher, generateTestToken } from './helpers/testAuth';
import SSEService from '../app/services/sse/SSEService';
import PollingService from '../app/services/polling/PollingService';
import { realTimeAnalyticsService } from '../app/services/realtime/RealTimeAnalyticsService';
import { realTimeMessagingService } from '../app/services/realtime/RealTimeMessagingService';

describe('Real-Time System Integration', () => {
  let app: Express;
  let sseService: SSEService;
  let pollingService: PollingService;
  let studentUser: any;
  let teacherUser: any;
  let studentToken: string;
  let teacherToken: string;

  beforeEach(async () => {
    app = createTestApp();
    sseService = new SSEService();
    pollingService = new PollingService();
    
    studentUser = createTestUser();
    teacherUser = createTestTeacher();
    studentToken = generateTestToken(studentUser);
    teacherToken = generateTestToken(teacherUser);
  });

  afterEach(async () => {
    sseService.shutdown();
    pollingService.shutdown();
    realTimeAnalyticsService.shutdown();
    realTimeMessagingService.shutdown();
  });

  describe('SSE and Polling Integration', () => {
    it('should handle both SSE and polling for same user', async () => {
      // Create SSE connection
      const sseResponse = await request(app)
        .get('/api/sse/connect')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(sseResponse.headers['content-type']).toBe('text/event-stream');

      // Create polling subscription
      const pollingResponse = await request(app)
        .post('/api/polling/subscribe')
        .set('Authorization', `Bearer ${studentToken}`)
        .send({ types: ['test', 'notification'] })
        .expect(200);

      expect(pollingResponse.body.success).toBe(true);
      expect(pollingResponse.body.subscriptionId).toBeDefined();

      // Both services should be tracking the user
      const sseStats = sseService.getConnectionStats();
      const pollingStats = pollingService.getStats();

      expect(sseStats.activeConnections).toBe(1);
      expect(pollingStats.activeSubscriptions).toBe(1);
    });

    it('should deliver messages through both channels', async () => {
      // Set up SSE connection
      const mockSSEResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const sseClientId = sseService.createConnection(
        {} as any,
        mockSSEResponse,
        studentUser.id,
        'student'
      );

      // Set up polling subscription
      const pollingSubId = pollingService.subscribe(studentUser.id, 'student', ['test']);

      // Send message through SSE
      const sseMessage = {
        id: 'sse-test-1',
        type: 'test',
        data: { message: 'SSE test message' },
        timestamp: new Date(),
        priority: 'medium' as const
      };

      sseService.sendToUser(studentUser.id, sseMessage);
      expect(mockSSEResponse.write).toHaveBeenCalled();

      // Send message through polling
      pollingService.addUpdate({
        id: 'polling-test-1',
        type: 'test',
        data: { message: 'Polling test message' },
        timestamp: new Date(),
        userId: studentUser.id,
        priority: 'medium' as const
      });

      const batch = pollingService.poll(pollingSubId);
      expect(batch!.updates).toHaveLength(1);
      expect(batch!.updates[0].data.message).toBe('Polling test message');
    });
  });

  describe('Analytics Integration', () => {
    it('should broadcast analytics updates to teachers', () => {
      // Set up teacher SSE connection
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      sseService.createConnection(
        {} as any,
        mockResponse,
        teacherUser.id,
        'teacher'
      );

      // Broadcast analytics update
      realTimeAnalyticsService.broadcastEnrollmentUpdate(teacherUser.id, {
        courseId: 'course123',
        newEnrollments: 5,
        totalEnrollments: 25
      });

      // Should have sent message via SSE
      expect(mockResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('enrollment')
      );
    });

    it('should handle batch analytics updates', () => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      sseService.createConnection(
        {} as any,
        mockResponse,
        teacherUser.id,
        'teacher'
      );

      // Send multiple analytics updates
      realTimeAnalyticsService.broadcastEnrollmentUpdate(teacherUser.id, { enrollments: 1 });
      realTimeAnalyticsService.broadcastRevenueUpdate(teacherUser.id, { revenue: 100 });
      realTimeAnalyticsService.broadcastPerformanceUpdate(teacherUser.id, { performance: 95 });

      // Trigger batch processing
      realTimeAnalyticsService.sendBatchUpdates();

      // Should have sent batch update
      expect(mockResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('analytics_batch')
      );
    });
  });

  describe('Messaging Integration', () => {
    it('should broadcast new messages to conversation participants', () => {
      // Set up connections for both users
      const studentResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const teacherResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const studentClientId = sseService.createConnection(
        {} as any,
        studentResponse,
        studentUser.id,
        'student'
      );

      const teacherClientId = sseService.createConnection(
        {} as any,
        teacherResponse,
        teacherUser.id,
        'teacher'
      );

      // Join conversation room
      sseService.joinRoom(studentClientId, 'conversation:conv123');
      sseService.joinRoom(teacherClientId, 'conversation:conv123');

      // Broadcast new message
      realTimeMessagingService.broadcastNewMessage('conv123', {
        id: 'msg123',
        content: 'Hello from student',
        senderId: studentUser.id,
        senderType: 'student',
        recipientIds: [teacherUser.id]
      });

      // Both users should receive the message
      expect(studentResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('new_message')
      );
      expect(teacherResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('new_message')
      );
    });

    it('should handle typing indicators', () => {
      const mockResponse = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        mockResponse,
        teacherUser.id,
        'teacher'
      );

      sseService.joinRoom(clientId, 'conversation:conv123');

      // Start typing
      realTimeMessagingService.handleTypingIndicator('conv123', studentUser.id, true);

      expect(mockResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('typing_start')
      );

      // Stop typing
      realTimeMessagingService.handleTypingIndicator('conv123', studentUser.id, false);

      expect(mockResponse.write).toHaveBeenCalledWith(
        expect.stringContaining('typing_stop')
      );
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle SSE connection failures gracefully', () => {
      const faultyResponse = {
        writeHead: jest.fn(),
        write: jest.fn().mockImplementation(() => {
          throw new Error('Connection lost');
        }),
        end: jest.fn()
      };

      const clientId = sseService.createConnection(
        {} as any,
        faultyResponse,
        studentUser.id,
        'student'
      );

      // Attempt to send message
      const message = {
        id: 'error-test',
        type: 'test',
        data: { message: 'This should fail' },
        timestamp: new Date(),
        priority: 'low' as const
      };

      const success = sseService.sendToClient(clientId, message);
      expect(success).toBe(false);

      // Connection should be cleaned up
      const stats = sseService.getConnectionStats();
      expect(stats.activeConnections).toBe(0);
    });

    it('should handle polling subscription failures', () => {
      const subscriptionId = pollingService.subscribe(studentUser.id, 'student');
      
      // Simulate multiple failures
      const subscription = (pollingService as any).subscriptions.get(subscriptionId);
      if (subscription) {
        for (let i = 0; i < 6; i++) {
          (pollingService as any).handlePollingFailure(subscription);
        }
        
        expect(subscription.isActive).toBe(false);
      }

      const stats = pollingService.getStats();
      expect(stats.activeSubscriptions).toBe(0);
    });

    it('should maintain service availability during high load', async () => {
      const numConnections = 100;
      const connections = [];

      // Create many SSE connections
      for (let i = 0; i < numConnections; i++) {
        const mockResponse = {
          writeHead: jest.fn(),
          write: jest.fn(),
          end: jest.fn()
        };

        const clientId = sseService.createConnection(
          {} as any,
          mockResponse,
          `user-${i}`,
          'student'
        );

        connections.push({ clientId, response: mockResponse });
      }

      // Create many polling subscriptions
      const subscriptions = [];
      for (let i = 0; i < numConnections; i++) {
        const subId = pollingService.subscribe(`user-${i}`, 'student');
        subscriptions.push(subId);
      }

      // Broadcast message to all
      const message = {
        id: 'load-test',
        type: 'broadcast',
        data: { message: 'Load test message' },
        timestamp: new Date(),
        priority: 'low' as const
      };

      const startTime = Date.now();
      const sseCount = sseService.broadcast(message);
      const sseTime = Date.now() - startTime;

      expect(sseCount).toBe(numConnections);
      expect(sseTime).toBeLessThan(1000); // Should complete within 1 second

      // Add polling updates
      const pollStartTime = Date.now();
      for (let i = 0; i < numConnections; i++) {
        pollingService.addUpdate({
          id: `poll-load-${i}`,
          type: 'test',
          data: { index: i },
          timestamp: new Date(),
          userId: `user-${i}`,
          priority: 'low' as const
        });
      }
      const pollTime = Date.now() - pollStartTime;

      expect(pollTime).toBeLessThan(1000); // Should complete within 1 second

      // Verify all connections still work
      const sseStats = sseService.getConnectionStats();
      const pollingStats = pollingService.getStats();

      expect(sseStats.activeConnections).toBe(numConnections);
      expect(pollingStats.activeSubscriptions).toBe(numConnections);
    });
  });

  describe('Monitoring Integration', () => {
    it('should provide comprehensive health status', async () => {
      // Set up some connections
      sseService.createConnection(
        { writeHead: jest.fn(), write: jest.fn(), end: jest.fn() } as any,
        {} as any,
        studentUser.id,
        'student'
      );

      pollingService.subscribe(teacherUser.id, 'teacher');

      const response = await request(app)
        .get('/api/monitoring/health')
        .expect(200);

      expect(response.body.status).toBeDefined();
      expect(response.body.services).toBeDefined();
      expect(response.body.services.sse).toBeDefined();
      expect(response.body.services.polling).toBeDefined();
      expect(response.body.system).toBeDefined();
    });

    it('should track performance metrics', async () => {
      const response = await request(app)
        .get('/api/monitoring/performance')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.system).toBeDefined();
      expect(response.body.data.realTime).toBeDefined();
      expect(response.body.data.realTime.sse).toBeDefined();
      expect(response.body.data.realTime.polling).toBeDefined();
    });
  });
});
