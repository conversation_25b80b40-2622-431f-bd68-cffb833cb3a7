import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { useAppSelector } from '@/redux/hooks';
import { selectCurrentUser, selectCurrentToken } from '@/redux/features/auth/authSlice';
import { config } from '@/config';

export interface SSEMessage {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  room?: string;
}

export interface SSEConnectionStats {
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  reconnectAttempts: number;
  lastMessage: SSEMessage | null;
  connectionTime: Date | null;
  messagesReceived: number;
}

export interface UseSSEOptions {
  autoConnect?: boolean;
  reconnectOnError?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Event) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export const useServerSentEvents = (options: UseSSEOptions = {}) => {
  const {
    autoConnect = true,
    reconnectOnError = true,
    maxReconnectAttempts = 5,
    reconnectDelay = 1000,
    onMessage,
    onError,
    onConnect,
    onDisconnect
  } = options;

  const user = useAppSelector(selectCurrentUser);
  const token = useAppSelector(selectCurrentToken);
  const [stats, setStats] = useState<SSEConnectionStats>({
    isConnected: false,
    isConnecting: false,
    connectionError: null,
    reconnectAttempts: 0,
    lastMessage: null,
    connectionTime: null,
    messagesReceived: 0
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUnmountingRef = useRef(false);

  const API_BASE_URL = config.apiBaseUrl || 'http://localhost:5000/api';

  /**
   * Connect to SSE endpoint
   */
  const connect = useCallback(() => {
    if (!user || !token || isUnmountingRef.current) {
      console.warn('SSE connection skipped: Missing user or token');
      return;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setStats(prev => ({
      ...prev,
      isConnecting: true,
      connectionError: null
    }));

    try {
      const url = new URL(`${API_BASE_URL}/sse/connect`);
      url.searchParams.append('token', token);

      const eventSource = new EventSource(url.toString());
      eventSourceRef.current = eventSource;

      // Connection opened
      eventSource.onopen = () => {
        if (isUnmountingRef.current) return;

        setStats(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          connectionError: null,
          reconnectAttempts: 0,
          connectionTime: new Date()
        }));

        onConnect?.();
        
        toast.success('Connected to real-time updates', {
          duration: 2000,
        });
      };

      // Message received
      eventSource.onmessage = (event) => {
        if (isUnmountingRef.current) return;

        try {
          const message: SSEMessage = JSON.parse(event.data);
          
          setStats(prev => ({
            ...prev,
            lastMessage: message,
            messagesReceived: prev.messagesReceived + 1
          }));

          onMessage?.(message);

          // Handle specific message types
          handleSSEMessage(message);
        } catch (error) {
          console.error('Failed to parse SSE message:', error);
        }
      };

      // Connection error
      eventSource.onerror = (error) => {
        if (isUnmountingRef.current) return;

        console.error('SSE connection error:', error);
        
        setStats(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          connectionError: 'Connection failed'
        }));

        onError?.(error);
        
        // Attempt reconnection
        if (reconnectOnError && stats.reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect();
        } else {
          toast.error('Real-time connection lost', {
            description: 'Please refresh the page to reconnect',
            duration: 5000,
          });
        }
      };

      // Handle specific event types
      eventSource.addEventListener('heartbeat', (event) => {
        // Heartbeat received - connection is alive
        console.debug('SSE heartbeat received');
      });

      eventSource.addEventListener('error', (event) => {
        if (isUnmountingRef.current) return;

        try {
          const errorData = JSON.parse((event as MessageEvent).data);
          console.error('SSE error event:', errorData);
          
          setStats(prev => ({
            ...prev,
            connectionError: errorData.message || 'Server error'
          }));

          toast.error('Connection error', {
            description: errorData.message || 'Server error occurred',
            duration: 3000,
          });
        } catch (error) {
          console.error('Failed to parse SSE error event:', error);
        }
      });

    } catch (error) {
      console.error('Failed to create SSE connection:', error);
      
      setStats(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        connectionError: 'Failed to connect'
      }));
    }
  }, [user, token, onConnect, onMessage, onError, reconnectOnError, maxReconnectAttempts, API_BASE_URL]);

  /**
   * Disconnect from SSE
   */
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setStats(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      connectionTime: null
    }));

    onDisconnect?.();
  }, [onDisconnect]);

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = reconnectDelay * Math.pow(2, stats.reconnectAttempts); // Exponential backoff
    
    setStats(prev => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      if (!isUnmountingRef.current) {
        connect();
      }
    }, Math.min(delay, 30000)); // Max 30 seconds

    toast.info(`Reconnecting in ${Math.round(delay / 1000)} seconds...`, {
      duration: 2000,
    });
  }, [connect, reconnectDelay, stats.reconnectAttempts]);

  /**
   * Handle specific SSE message types
   */
  const handleSSEMessage = useCallback((message: SSEMessage) => {
    switch (message.type) {
      case 'notification':
        handleNotification(message);
        break;
      case 'analytics_update':
        handleAnalyticsUpdate(message);
        break;
      case 'new_message':
        handleNewMessage(message);
        break;
      case 'course_update':
        handleCourseUpdate(message);
        break;
      case 'system_alert':
        handleSystemAlert(message);
        break;
      default:
        console.debug('Unhandled SSE message type:', message.type);
    }
  }, []);

  const handleNotification = (message: SSEMessage) => {
    const { data } = message;
    
    const toastOptions = {
      duration: data.priority === 'urgent' ? 10000 : 5000,
      action: data.actionUrl ? {
        label: data.actionText || 'View',
        onClick: () => window.open(data.actionUrl, '_blank'),
      } : undefined,
    };

    switch (data.type) {
      case 'success':
        toast.success(data.title, { description: data.message, ...toastOptions });
        break;
      case 'error':
        toast.error(data.title, { description: data.message, ...toastOptions });
        break;
      case 'warning':
        toast.warning(data.title, { description: data.message, ...toastOptions });
        break;
      default:
        toast.info(data.title, { description: data.message, ...toastOptions });
    }
  };

  const handleAnalyticsUpdate = (message: SSEMessage) => {
    // Dispatch custom event for analytics components to listen to
    window.dispatchEvent(new CustomEvent('analytics-update', {
      detail: message.data
    }));
  };

  const handleNewMessage = (message: SSEMessage) => {
    // Dispatch custom event for messaging components
    window.dispatchEvent(new CustomEvent('new-message', {
      detail: message.data
    }));
    
    // Show notification for new messages
    if (message.data.senderId !== user?.id) {
      toast.info('New message', {
        description: `From ${message.data.senderName || 'Unknown'}`,
        duration: 3000,
      });
    }
  };

  const handleCourseUpdate = (message: SSEMessage) => {
    // Dispatch custom event for course components
    window.dispatchEvent(new CustomEvent('course-update', {
      detail: message.data
    }));
  };

  const handleSystemAlert = (message: SSEMessage) => {
    const { data } = message;
    
    toast.warning(data.title, {
      description: data.message,
      duration: data.severity === 'high' ? 10000 : 5000,
    });
  };

  /**
   * Manual reconnect
   */
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      if (!isUnmountingRef.current) {
        connect();
      }
    }, 1000);
  }, [connect, disconnect]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && user && token) {
      connect();
    }

    return () => {
      isUnmountingRef.current = true;
      disconnect();
    };
  }, [autoConnect, user, token, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountingRef.current = true;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...stats,
    connect,
    disconnect,
    reconnect,
    // Additional utility methods
    sendMessage: (type: string, data: any) => {
      // SSE is read-only, so this would need to use HTTP API
      console.warn('SSE is read-only. Use HTTP API for sending messages.');
    },
    joinRoom: (roomName: string) => {
      // Room joining would be handled via HTTP API
      console.debug('Room joining via SSE - handled server-side');
    },
    leaveRoom: (roomName: string) => {
      // Room leaving would be handled via HTTP API
      console.debug('Room leaving via SSE - handled server-side');
    }
  };
};
