import { Logger } from '../../config/logger';
import { Response } from 'express';
import { AuthenticatedSSERequest } from '../../middlewares/sseMiddleware';
import { sseRetryManager, RetryPolicy } from './SSERetryManager';
import { authAuditService } from '../audit/AuthAuditService';

export interface SSEError {
  code: string;
  type: 'temporary' | 'permanent' | 'recoverable';
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'auth' | 'connection' | 'network' | 'server' | 'client';
  message: string;
  retryable: boolean;
  retryPolicy?: RetryPolicy;
  fallbackAction?: 'polling' | 'reconnect' | 'refresh_auth' | 'none';
  userMessage: string;
  technicalDetails?: Record<string, any>;
}

export interface ErrorResponse {
  id: string;
  timestamp: Date;
  error: {
    code: string;
    type: string;
    category: string;
    message: string;
    userMessage: string;
    retryable: boolean;
    retryAfter?: number;
    fallbackAction?: string;
  };
  instructions: {
    immediate: string;
    fallback?: string;
    prevention?: string;
  };
  metadata: {
    connectionId?: string;
    userId?: string;
    clientInfo?: Record<string, any>;
  };
}

export class SSEErrorHandler {
  private readonly errorCatalog: Map<string, SSEError> = new Map();

  constructor() {
    this.initializeErrorCatalog();
  }

  /**
   * Handle SSE error with comprehensive categorization and response
   */
  async handleError(
    error: any,
    req: AuthenticatedSSERequest,
    res: Response,
    context?: Record<string, any>
  ): Promise<void> {
    const sseError = this.categorizeError(error);
    const clientId = this.generateClientId(req);
    
    // Record error for analytics
    await this.recordError(sseError, req, context);
    
    // Check retry eligibility
    const retryCheck = sseRetryManager.shouldRetry(
      clientId,
      sseError.category,
      sseError.retryPolicy
    );

    // Record retry attempt if applicable
    if (retryCheck.canRetry && sseError.retryable) {
      await sseRetryManager.recordRetryAttempt(
        clientId,
        sseError.category as any,
        sseError.message,
        sseError.retryPolicy
      );
    }

    // Generate error response
    const errorResponse = this.createErrorResponse(sseError, req, retryCheck);
    
    // Send appropriate response based on error type
    await this.sendErrorResponse(errorResponse, res, sseError);
    
    // Log error with appropriate severity
    this.logError(sseError, req, context);
  }

  /**
   * Categorize error based on type and context
   */
  private categorizeError(error: any): SSEError {
    // Check for known error patterns
    if (error instanceof Error) {
      // JWT/Authentication errors
      if (error.name === 'TokenExpiredError') {
        return this.errorCatalog.get('TOKEN_EXPIRED')!;
      }
      if (error.name === 'JsonWebTokenError') {
        return this.errorCatalog.get('TOKEN_INVALID')!;
      }
      if (error.message.includes('refresh')) {
        return this.errorCatalog.get('TOKEN_REFRESH_FAILED')!;
      }
      
      // Network/Connection errors
      if (error.message.includes('ECONNRESET') || error.message.includes('ENOTFOUND')) {
        return this.errorCatalog.get('NETWORK_ERROR')!;
      }
      if (error.message.includes('timeout')) {
        return this.errorCatalog.get('CONNECTION_TIMEOUT')!;
      }
      
      // Rate limiting
      if (error.message.includes('rate limit') || error.message.includes('too many')) {
        return this.errorCatalog.get('RATE_LIMITED')!;
      }
    }

    // Default to generic server error
    return this.errorCatalog.get('GENERIC_ERROR')!;
  }

  /**
   * Create structured error response
   */
  private createErrorResponse(
    sseError: SSEError,
    req: AuthenticatedSSERequest,
    retryCheck: { canRetry: boolean; retryAfter?: number; reason?: string }
  ): ErrorResponse {
    const errorId = `sse_error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    return {
      id: errorId,
      timestamp: new Date(),
      error: {
        code: sseError.code,
        type: sseError.type,
        category: sseError.category,
        message: sseError.message,
        userMessage: sseError.userMessage,
        retryable: sseError.retryable && retryCheck.canRetry,
        retryAfter: retryCheck.retryAfter,
        fallbackAction: sseError.fallbackAction
      },
      instructions: {
        immediate: this.getImmediateInstructions(sseError, retryCheck),
        fallback: this.getFallbackInstructions(sseError),
        prevention: this.getPreventionInstructions(sseError)
      },
      metadata: {
        connectionId: req.connectionId,
        userId: req.user?._id,
        clientInfo: {
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          endpoint: req.originalUrl
        }
      }
    };
  }

  /**
   * Send error response via SSE
   */
  private async sendErrorResponse(
    errorResponse: ErrorResponse,
    res: Response,
    sseError: SSEError
  ): Promise<void> {
    const statusCode = this.getHttpStatusCode(sseError);
    
    // Set appropriate headers
    res.writeHead(statusCode, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': 'true',
      'X-Error-Code': sseError.code,
      'X-Error-Type': sseError.type,
      'X-Error-Category': sseError.category,
      'X-Retryable': sseError.retryable.toString(),
      ...(errorResponse.error.retryAfter && { 'Retry-After': errorResponse.error.retryAfter.toString() }),
      ...(sseError.fallbackAction && { 'X-Fallback-Action': sseError.fallbackAction })
    });

    // Send error event
    const eventData = `event: error\ndata: ${JSON.stringify(errorResponse)}\n\n`;
    res.write(eventData);
    
    // Close connection for permanent errors
    if (sseError.type === 'permanent') {
      res.end();
    }
  }

  /**
   * Record error for analytics and monitoring
   */
  private async recordError(
    sseError: SSEError,
    req: AuthenticatedSSERequest,
    context?: Record<string, any>
  ): Promise<void> {
    try {
      await authAuditService.logAuthFailure(
        req.user?.email,
        'sse',
        sseError.code,
        sseError.message,
        {
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          endpoint: req.originalUrl
        }
      );
    } catch (error) {
      Logger.error('Failed to record error in audit service:', error);
    }
  }

  /**
   * Log error with appropriate severity
   */
  private logError(
    sseError: SSEError,
    req: AuthenticatedSSERequest,
    context?: Record<string, any>
  ): void {
    const logData = {
      errorCode: sseError.code,
      errorType: sseError.type,
      category: sseError.category,
      severity: sseError.severity,
      userId: req.user?._id,
      connectionId: req.connectionId,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      context
    };

    switch (sseError.severity) {
      case 'critical':
        Logger.error(`🚨 CRITICAL SSE Error: ${sseError.message}`, logData);
        break;
      case 'high':
        Logger.error(`❌ HIGH SSE Error: ${sseError.message}`, logData);
        break;
      case 'medium':
        Logger.warn(`⚠️ MEDIUM SSE Error: ${sseError.message}`, logData);
        break;
      case 'low':
        Logger.info(`ℹ️ LOW SSE Error: ${sseError.message}`, logData);
        break;
    }
  }

  /**
   * Get immediate action instructions
   */
  private getImmediateInstructions(
    sseError: SSEError,
    retryCheck: { canRetry: boolean; retryAfter?: number; reason?: string }
  ): string {
    if (sseError.retryable && retryCheck.canRetry) {
      return `Please retry in ${retryCheck.retryAfter} seconds`;
    }
    
    if (sseError.fallbackAction) {
      switch (sseError.fallbackAction) {
        case 'polling':
          return 'Switching to polling mode for real-time updates';
        case 'reconnect':
          return 'Please refresh the page to reconnect';
        case 'refresh_auth':
          return 'Please refresh your authentication and try again';
        default:
          return 'Please try again later';
      }
    }
    
    return 'Please contact support if the issue persists';
  }

  /**
   * Get fallback action instructions
   */
  private getFallbackInstructions(sseError: SSEError): string | undefined {
    switch (sseError.fallbackAction) {
      case 'polling':
        return 'The application will automatically switch to polling mode to continue providing updates';
      case 'reconnect':
        return 'Refreshing the page will establish a new connection';
      case 'refresh_auth':
        return 'Please log out and log back in to refresh your authentication';
      default:
        return undefined;
    }
  }

  /**
   * Get prevention instructions
   */
  private getPreventionInstructions(sseError: SSEError): string | undefined {
    switch (sseError.category) {
      case 'auth':
        return 'Ensure you stay logged in and refresh your session regularly';
      case 'network':
        return 'Check your internet connection stability';
      case 'connection':
        return 'Avoid opening multiple tabs with the same application';
      default:
        return undefined;
    }
  }

  /**
   * Get HTTP status code for error
   */
  private getHttpStatusCode(sseError: SSEError): number {
    switch (sseError.category) {
      case 'auth':
        return 401;
      case 'client':
        return 400;
      case 'server':
        return 500;
      case 'network':
        return 503;
      default:
        return 500;
    }
  }

  /**
   * Generate client ID for tracking
   */
  private generateClientId(req: AuthenticatedSSERequest): string {
    return `${req.ip}_${req.headers['user-agent']?.slice(0, 20) || 'unknown'}`;
  }

  /**
   * Initialize error catalog with predefined errors
   */
  private initializeErrorCatalog(): void {
    const errors: SSEError[] = [
      {
        code: 'TOKEN_EXPIRED',
        type: 'recoverable',
        severity: 'medium',
        category: 'auth',
        message: 'Access token has expired',
        retryable: true,
        retryPolicy: sseRetryManager.constructor.createPolicy('auth'),
        fallbackAction: 'refresh_auth',
        userMessage: 'Your session has expired. Please refresh your authentication.'
      },
      {
        code: 'TOKEN_INVALID',
        type: 'permanent',
        severity: 'high',
        category: 'auth',
        message: 'Invalid access token',
        retryable: false,
        fallbackAction: 'refresh_auth',
        userMessage: 'Authentication failed. Please log in again.'
      },
      {
        code: 'TOKEN_REFRESH_FAILED',
        type: 'permanent',
        severity: 'high',
        category: 'auth',
        message: 'Failed to refresh access token',
        retryable: false,
        fallbackAction: 'refresh_auth',
        userMessage: 'Unable to refresh your session. Please log in again.'
      },
      {
        code: 'NETWORK_ERROR',
        type: 'temporary',
        severity: 'medium',
        category: 'network',
        message: 'Network connectivity issue',
        retryable: true,
        retryPolicy: sseRetryManager.constructor.createPolicy('connection'),
        fallbackAction: 'polling',
        userMessage: 'Connection issue detected. Switching to backup mode.'
      },
      {
        code: 'CONNECTION_TIMEOUT',
        type: 'temporary',
        severity: 'medium',
        category: 'connection',
        message: 'Connection timeout',
        retryable: true,
        retryPolicy: sseRetryManager.constructor.createPolicy('connection'),
        fallbackAction: 'reconnect',
        userMessage: 'Connection timed out. Please refresh to reconnect.'
      },
      {
        code: 'RATE_LIMITED',
        type: 'temporary',
        severity: 'low',
        category: 'client',
        message: 'Rate limit exceeded',
        retryable: true,
        retryPolicy: sseRetryManager.constructor.createPolicy('connection'),
        fallbackAction: 'polling',
        userMessage: 'Too many requests. Please wait before trying again.'
      },
      {
        code: 'GENERIC_ERROR',
        type: 'temporary',
        severity: 'medium',
        category: 'server',
        message: 'An unexpected error occurred',
        retryable: true,
        retryPolicy: sseRetryManager.constructor.createPolicy('connection'),
        fallbackAction: 'polling',
        userMessage: 'Something went wrong. We\'re working to fix it.'
      }
    ];

    errors.forEach(error => {
      this.errorCatalog.set(error.code, error);
    });
  }
}

// Export singleton instance
export const sseErrorHandler = new SSEErrorHandler();
