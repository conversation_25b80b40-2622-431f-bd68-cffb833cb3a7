import { Logger } from '../../config/logger';
import { serviceRegistry } from '../ServiceRegistry';

export interface AnalyticsUpdate {
  type: 'enrollment' | 'revenue' | 'performance' | 'engagement' | 'dashboard' | 'course_update' | 'lecture_update';
  data: any;
  teacherId: string;
  courseId?: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface BatchAnalyticsUpdate {
  updates: AnalyticsUpdate[];
  batchId: string;
  timestamp: Date;
  totalUpdates: number;
}

class RealTimeAnalyticsService {
  private batchQueue: Map<string, AnalyticsUpdate[]> = new Map(); // teacherId -> updates
  private batchInterval: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 10;
  private readonly BATCH_INTERVAL = 5000; // 5 seconds
  private readonly UPDATE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    this.startBatchProcessing();
    Logger.info('📊 Real-Time Analytics Service initialized');
  }

  /**
   * Broadcast enrollment update to teacher
   */
  public broadcastEnrollmentUpdate(teacherId: string, data: any): void {
    const update: AnalyticsUpdate = {
      type: 'enrollment',
      data,
      teacherId,
      timestamp: new Date(),
      priority: 'medium'
    };

    this.addToBatch(update);
    this.sendImmediateUpdate(update);
  }

  /**
   * Broadcast revenue update to teacher
   */
  public broadcastRevenueUpdate(teacherId: string, data: any): void {
    const update: AnalyticsUpdate = {
      type: 'revenue',
      data,
      teacherId,
      timestamp: new Date(),
      priority: 'high'
    };

    this.addToBatch(update);
    this.sendImmediateUpdate(update);
  }

  /**
   * Broadcast performance update to teacher
   */
  public broadcastPerformanceUpdate(teacherId: string, data: any): void {
    const update: AnalyticsUpdate = {
      type: 'performance',
      data,
      teacherId,
      timestamp: new Date(),
      priority: 'medium'
    };

    this.addToBatch(update);
    this.sendImmediateUpdate(update);
  }

  /**
   * Broadcast engagement update to teacher
   */
  public broadcastEngagementUpdate(teacherId: string, data: any): void {
    const update: AnalyticsUpdate = {
      type: 'engagement',
      data,
      teacherId,
      timestamp: new Date(),
      priority: 'medium'
    };

    this.addToBatch(update);
    this.sendImmediateUpdate(update);
  }

  /**
   * Broadcast dashboard update to teacher
   */
  public broadcastDashboardUpdate(teacherId: string, data: any): void {
    const update: AnalyticsUpdate = {
      type: 'dashboard',
      data,
      teacherId,
      timestamp: new Date(),
      priority: 'medium'
    };

    this.addToBatch(update);
    this.sendImmediateUpdate(update);
  }

  /**
   * Broadcast course update to all relevant users
   */
  public broadcastCourseUpdate(courseId: string, data: any, teacherId?: string): void {
    const update: AnalyticsUpdate = {
      type: 'course_update',
      data: {
        ...data,
        courseId
      },
      teacherId: teacherId || '',
      courseId,
      timestamp: new Date(),
      priority: 'medium'
    };

    // Send to teacher if specified
    if (teacherId) {
      this.addToBatch(update);
      this.sendImmediateUpdate(update);
    }

    // Send to course room via SSE
    this.sendToRoom(`course:${courseId}`, update);
  }

  /**
   * Broadcast lecture update to course participants
   */
  public broadcastLectureUpdate(courseId: string, data: any, teacherId?: string): void {
    const update: AnalyticsUpdate = {
      type: 'lecture_update',
      data: {
        ...data,
        courseId
      },
      teacherId: teacherId || '',
      courseId,
      timestamp: new Date(),
      priority: 'medium'
    };

    // Send to teacher if specified
    if (teacherId) {
      this.addToBatch(update);
      this.sendImmediateUpdate(update);
    }

    // Send to course room via SSE
    this.sendToRoom(`course:${courseId}`, update);
  }

  /**
   * Send batch updates to teachers
   */
  public sendBatchUpdates(): void {
    for (const [teacherId, updates] of this.batchQueue.entries()) {
      if (updates.length === 0) continue;

      const batch: BatchAnalyticsUpdate = {
        updates: [...updates],
        batchId: `batch_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: new Date(),
        totalUpdates: updates.length
      };

      // Send via SSE
      this.sendSSEUpdate(teacherId, {
        type: 'analytics_batch',
        data: batch,
        priority: 'medium'
      });

      // Send via Polling
      this.sendPollingUpdate(teacherId, {
        type: 'analytics_batch',
        data: batch,
        priority: 'medium'
      });

      // Clear processed updates
      updates.length = 0;

      Logger.debug(`📊 Sent batch analytics update to teacher ${teacherId}: ${batch.totalUpdates} updates`);
    }
  }

  /**
   * Get analytics statistics
   */
  public getAnalyticsStats(): {
    queuedUpdates: number;
    teachersWithUpdates: number;
    batchesProcessed: number;
  } {
    const queuedUpdates = Array.from(this.batchQueue.values())
      .reduce((total, updates) => total + updates.length, 0);
    
    const teachersWithUpdates = Array.from(this.batchQueue.values())
      .filter(updates => updates.length > 0).length;

    return {
      queuedUpdates,
      teachersWithUpdates,
      batchesProcessed: 0 // This would be tracked in a real implementation
    };
  }

  /**
   * Private helper methods
   */
  private addToBatch(update: AnalyticsUpdate): void {
    if (!this.batchQueue.has(update.teacherId)) {
      this.batchQueue.set(update.teacherId, []);
    }

    const queue = this.batchQueue.get(update.teacherId)!;
    queue.push(update);

    // Limit queue size
    if (queue.length > this.BATCH_SIZE * 2) {
      queue.splice(0, queue.length - this.BATCH_SIZE * 2);
    }
  }

  private sendImmediateUpdate(update: AnalyticsUpdate): void {
    // Send high priority updates immediately
    if (update.priority === 'high' || update.priority === 'urgent') {
      this.sendSSEUpdate(update.teacherId, {
        type: update.type,
        data: update.data,
        priority: update.priority
      });

      this.sendPollingUpdate(update.teacherId, {
        type: update.type,
        data: update.data,
        priority: update.priority
      });
    }
  }

  private sendSSEUpdate(userId: string, update: { type: string; data: any; priority: string }): void {
    try {
      const sseMessage = {
        id: `sse_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        type: update.type,
        data: update.data,
        timestamp: new Date(),
        targetUserId: userId,
        priority: update.priority as 'low' | 'medium' | 'high' | 'urgent',
        expiresAt: new Date(Date.now() + this.UPDATE_EXPIRY)
      };

      serviceRegistry.getSSEService().sendToUser(userId, sseMessage);
    } catch (error) {
      Logger.error('❌ Failed to send SSE analytics update:', error);
    }
  }

  private sendPollingUpdate(userId: string, update: { type: string; data: any; priority: string }): void {
    try {
      const pollingUpdate = {
        id: `poll_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        type: update.type,
        data: update.data,
        timestamp: new Date(),
        userId,
        priority: update.priority as 'low' | 'medium' | 'high' | 'urgent',
        expiresAt: new Date(Date.now() + this.UPDATE_EXPIRY)
      };

      serviceRegistry.getPollingService().addUpdate(pollingUpdate);
    } catch (error) {
      Logger.error('❌ Failed to send polling analytics update:', error);
    }
  }

  private sendToRoom(roomName: string, update: AnalyticsUpdate): void {
    try {
      // Send via SSE to room
      const sseMessage = {
        id: `room_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        type: update.type,
        data: update.data,
        timestamp: new Date(),
        room: roomName,
        priority: update.priority,
        expiresAt: new Date(Date.now() + this.UPDATE_EXPIRY)
      };

      serviceRegistry.getSSEService().sendToRoom(roomName, sseMessage);

      // Send via Polling to room
      serviceRegistry.getPollingService().broadcastToRoom(roomName, {
        type: update.type,
        data: update.data,
        priority: update.priority,
        expiresAt: new Date(Date.now() + this.UPDATE_EXPIRY)
      });
    } catch (error) {
      Logger.error('❌ Failed to send room analytics update:', error);
    }
  }

  private startBatchProcessing(): void {
    this.batchInterval = setInterval(() => {
      this.sendBatchUpdates();
    }, this.BATCH_INTERVAL);
  }

  /**
   * Shutdown service
   */
  public shutdown(): void {
    if (this.batchInterval) {
      clearInterval(this.batchInterval);
    }

    // Send any remaining batched updates
    this.sendBatchUpdates();

    this.batchQueue.clear();
    Logger.info('📊 Real-Time Analytics Service shutdown complete');
  }
}

// Create singleton instance
const realTimeAnalyticsService = new RealTimeAnalyticsService();

export { realTimeAnalyticsService };
export default RealTimeAnalyticsService;
