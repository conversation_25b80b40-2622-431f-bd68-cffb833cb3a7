import React, { useEffect, useState, useCallback } from 'react';
import { useRealTime } from '@/hooks/useRealTime';
import { useAppSelector } from '@/redux/hooks';
import { selectCurrentUser, selectCurrentToken } from '@/redux/features/auth/authSlice';
import { toast } from 'sonner';
import { Bell, Wifi, WifiOff, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Environment } from '@/utils/environment';

interface NotificationData {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  actionText?: string;
  category?: 'system' | 'course' | 'payment' | 'message' | 'analytics';
}

interface RealTimeNotificationsProps {
  showConnectionStatus?: boolean;
  showNotificationCount?: boolean;
  maxNotifications?: number;
  className?: string;
}

export const RealTimeNotifications: React.FC<RealTimeNotificationsProps> = ({
  showConnectionStatus = true,
  showNotificationCount = true,
  maxNotifications = 50,
  className
}) => {
  const user = useAppSelector(selectCurrentUser);
  const token = useAppSelector(selectCurrentToken);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Only initialize real-time connection if user is authenticated
  const realTime = useRealTime({
    preferSSE: true,
    enablePollingFallback: true,
    pollingTypes: ['notification', 'system_alert', 'course_update', 'payment_update', 'new_message'],
    autoConnect: !!user && !!token, // Only auto-connect if authenticated
    onUpdate: (update) => {
      handleRealTimeUpdate(update);
    },
    onConnectionChange: (stats) => {
      handleConnectionChange(stats);
    }
  });

  /**
   * Handle incoming real-time updates
   */
  const handleRealTimeUpdate = useCallback((update: any) => {
    const notification = createNotificationFromUpdate(update);
    if (notification) {
      addNotification(notification);
      showToastNotification(notification);
    }
  }, []);

  /**
   * Handle connection status changes
   */
  const handleConnectionChange = useCallback((stats: any) => {
    if (stats.isConnected && stats.connectionMethod) {
      toast.success('Real-time connection established', {
        description: `Connected via ${stats.connectionMethod.toUpperCase()}`,
        duration: 2000,
      });
    } else if (!stats.isConnected) {
      toast.error('Real-time connection lost', {
        description: 'Attempting to reconnect...',
        duration: 3000,
      });
    }
  }, []);

  /**
   * Create notification from real-time update
   */
  const createNotificationFromUpdate = (update: any): NotificationData | null => {
    const baseNotification: Partial<NotificationData> = {
      id: update.id || `notif_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date(update.timestamp),
      priority: update.priority || 'medium'
    };

    switch (update.type) {
      case 'notification':
        return {
          ...baseNotification,
          type: update.data.type || 'info',
          title: update.data.title || 'Notification',
          message: update.data.message || 'You have a new notification',
          actionUrl: update.data.actionUrl,
          actionText: update.data.actionText,
          category: update.data.category || 'system'
        } as NotificationData;

      case 'system_alert':
        return {
          ...baseNotification,
          type: 'warning',
          title: update.data.title || 'System Alert',
          message: update.data.message || 'System maintenance notification',
          category: 'system'
        } as NotificationData;

      case 'course_update':
        return {
          ...baseNotification,
          type: 'info',
          title: 'Course Update',
          message: `Course "${update.data.course?.title || 'Unknown'}" has been updated`,
          actionUrl: `/courses/${update.data.courseId}`,
          actionText: 'View Course',
          category: 'course'
        } as NotificationData;

      case 'payment_update':
        return {
          ...baseNotification,
          type: update.data.success ? 'success' : 'error',
          title: update.data.success ? 'Payment Successful' : 'Payment Failed',
          message: update.data.message || 'Payment status updated',
          actionUrl: '/dashboard/earnings',
          actionText: 'View Details',
          category: 'payment'
        } as NotificationData;

      case 'new_message':
        return {
          ...baseNotification,
          type: 'info',
          title: 'New Message',
          message: `New message from ${update.data.senderName || 'Unknown'}`,
          actionUrl: `/messages/${update.data.conversationId}`,
          actionText: 'View Message',
          category: 'message'
        } as NotificationData;

      default:
        return null;
    }
  };

  /**
   * Add notification to state
   */
  const addNotification = useCallback((notification: NotificationData) => {
    setNotifications(prev => {
      const newNotifications = [notification, ...prev].slice(0, maxNotifications);
      return newNotifications;
    });
    
    setUnreadCount(prev => prev + 1);
  }, [maxNotifications]);

  /**
   * Show toast notification
   */
  const showToastNotification = useCallback((notification: NotificationData) => {
    const toastOptions = {
      duration: notification.priority === 'urgent' ? 10000 : 
                notification.priority === 'high' ? 7000 : 5000,
      action: notification.actionUrl ? {
        label: notification.actionText || 'View',
        onClick: () => {
          if (notification.actionUrl) {
            window.open(notification.actionUrl, '_blank');
          }
        },
      } : undefined,
    };

    switch (notification.type) {
      case 'success':
        toast.success(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      case 'error':
        toast.error(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      case 'warning':
        toast.warning(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
        break;
      default:
        toast.info(notification.title, { 
          description: notification.message, 
          ...toastOptions 
        });
    }
  }, []);

  /**
   * Mark notifications as read
   */
  const markAsRead = useCallback(() => {
    setUnreadCount(0);
  }, []);

  /**
   * Clear all notifications
   */
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  /**
   * Get connection status icon
   */
  const getConnectionIcon = () => {
    if (!realTime.isConnected) {
      return <WifiOff className="h-4 w-4 text-red-500" />;
    }
    
    switch (realTime.connectionQuality) {
      case 'excellent':
        return <Wifi className="h-4 w-4 text-green-500" />;
      case 'good':
        return <Wifi className="h-4 w-4 text-blue-500" />;
      case 'poor':
        return <Wifi className="h-4 w-4 text-yellow-500" />;
      default:
        return <WifiOff className="h-4 w-4 text-red-500" />;
    }
  };

  /**
   * Get notification type icon
   */
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  // Listen for custom events from other parts of the application
  useEffect(() => {
    const handleCustomNotification = (event: CustomEvent) => {
      const notification = createNotificationFromUpdate(event.detail);
      if (notification) {
        addNotification(notification);
      }
    };

    window.addEventListener('analytics-update', handleCustomNotification);
    window.addEventListener('course-update', handleCustomNotification);
    window.addEventListener('new-message', handleCustomNotification);

    return () => {
      window.removeEventListener('analytics-update', handleCustomNotification);
      window.removeEventListener('course-update', handleCustomNotification);
      window.removeEventListener('new-message', handleCustomNotification);
    };
  }, [addNotification]);

  if (!user) {
    return null;
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      {/* Connection Status */}
      {showConnectionStatus && (
        <div 
          className="flex items-center space-x-1 px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-800"
          title={`Connection: ${realTime.connectionMethod} (${realTime.connectionQuality})`}
        >
          {getConnectionIcon()}
          <span className="text-xs text-gray-600 dark:text-gray-400">
            {realTime.connectionMethod}
          </span>
        </div>
      )}

      {/* Notification Bell */}
      {showNotificationCount && (
        <div className="relative">
          <button
            onClick={markAsRead}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            title="Notifications"
          >
            <Bell className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>
        </div>
      )}

      {/* Debug Info (only in development) */}
      {Environment.isDevelopment() && (
        <div className="text-xs text-gray-500 space-y-1">
          <div>SSE: {realTime.sse.isConnected ? '✓' : '✗'}</div>
          <div>Polling: {realTime.polling.isActive ? '✓' : '✗'}</div>
          <div>Quality: {realTime.connectionQuality}</div>
        </div>
      )}
    </div>
  );
};

export default RealTimeNotifications;
