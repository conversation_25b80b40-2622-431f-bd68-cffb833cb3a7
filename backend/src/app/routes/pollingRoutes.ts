import { Router, Request, Response } from 'express';

const router = Router();

// TEMPORARY: All polling routes disabled to stop infinite loop
// TODO: Re-enable after frontend fixes are deployed and browser caches cleared

const disabledResponse = {
  error: 'Service temporarily unavailable',
  message: 'Polling service is temporarily disabled for maintenance',
  canRetry: false,
  fallbackToSSE: false
};

/**
 * Subscribe to polling updates
 * POST /api/polling/subscribe
 */
router.post('/subscribe', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Unsubscribe from polling updates
 * POST /api/polling/unsubscribe
 */
router.post('/unsubscribe', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Poll for updates
 * GET /api/polling/poll/:subscriptionId
 */
router.get('/poll/:subscriptionId', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Add update for specific user
 * POST /api/polling/add-update
 */
router.post('/add-update', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Broadcast update to all users
 * POST /api/polling/broadcast
 */
router.post('/broadcast', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Broadcast update to room
 * POST /api/polling/broadcast-to-room
 */
router.post('/broadcast-to-room', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

/**
 * Get polling statistics
 * GET /api/polling/stats
 */
router.get('/stats', (req: Request, res: Response) => {
  return res.status(503).json(disabledResponse);
});

export default router;
