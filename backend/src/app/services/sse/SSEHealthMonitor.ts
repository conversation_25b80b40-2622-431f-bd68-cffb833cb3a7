import { Logger } from '../../config/logger';
import { redisOperations } from '../../config/redis';

export interface SSEConnectionHealth {
  connectionId: string;
  userId: string;
  userRole: string;
  connectedAt: Date;
  lastHeartbeat: Date;
  lastActivity: Date;
  isHealthy: boolean;
  latency: number;
  errorCount: number;
  reconnectCount: number;
  clientInfo: {
    ip: string;
    userAgent: string;
    endpoint: string;
  };
}

export interface SSEHealthMetrics {
  totalConnections: number;
  healthyConnections: number;
  unhealthyConnections: number;
  averageLatency: number;
  totalErrors: number;
  totalReconnects: number;
  connectionsByRole: Record<string, number>;
  connectionsByStatus: {
    connected: number;
    disconnected: number;
    error: number;
  };
}

export class SSEHealthMonitor {
  private connections: Map<string, SSEConnectionHealth> = new Map();
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private readonly CONNECTION_TIMEOUT = 300000; // 5 minutes
  private readonly HEALTH_CHECK_INTERVAL = 60000; // 1 minute
  private readonly REDIS_KEY_PREFIX = 'sse:health:';
  
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startMonitoring();
    Logger.info('📊 SSE Health Monitor initialized');
  }

  /**
   * Register a new SSE connection for monitoring
   */
  async registerConnection(
    connectionId: string,
    userId: string,
    userRole: string,
    clientInfo: { ip: string; userAgent: string; endpoint: string }
  ): Promise<void> {
    const now = new Date();
    
    const connectionHealth: SSEConnectionHealth = {
      connectionId,
      userId,
      userRole,
      connectedAt: now,
      lastHeartbeat: now,
      lastActivity: now,
      isHealthy: true,
      latency: 0,
      errorCount: 0,
      reconnectCount: 0,
      clientInfo
    };

    this.connections.set(connectionId, connectionHealth);

    // Store in Redis for persistence
    await this.persistConnectionHealth(connectionHealth);

    Logger.info(`📡 SSE connection registered: ${connectionId} for user ${userId} (${userRole})`);
  }

  /**
   * Update connection activity
   */
  async updateActivity(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
      await this.persistConnectionHealth(connection);
    }
  }

  /**
   * Record heartbeat for connection
   */
  async recordHeartbeat(connectionId: string, latency?: number): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      const now = new Date();
      connection.lastHeartbeat = now;
      connection.lastActivity = now;
      
      if (latency !== undefined) {
        connection.latency = latency;
      }

      // Update health status based on latency and activity
      connection.isHealthy = this.calculateHealthStatus(connection);
      
      await this.persistConnectionHealth(connection);
    }
  }

  /**
   * Record error for connection
   */
  async recordError(connectionId: string, error: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.errorCount++;
      connection.isHealthy = this.calculateHealthStatus(connection);
      
      await this.persistConnectionHealth(connection);
      
      Logger.warn(`❌ SSE connection error: ${connectionId} - ${error}`);
    }
  }

  /**
   * Record reconnection
   */
  async recordReconnect(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.reconnectCount++;
      connection.lastActivity = new Date();
      connection.isHealthy = true; // Reset health on reconnect
      
      await this.persistConnectionHealth(connection);
      
      Logger.info(`🔄 SSE connection reconnected: ${connectionId}`);
    }
  }

  /**
   * Unregister connection
   */
  async unregisterConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      this.connections.delete(connectionId);
      
      // Remove from Redis
      await redisOperations.del(`${this.REDIS_KEY_PREFIX}${connectionId}`);
      
      Logger.info(`📡 SSE connection unregistered: ${connectionId}`);
    }
  }

  /**
   * Get health metrics for all connections
   */
  getHealthMetrics(): SSEHealthMetrics {
    const connections = Array.from(this.connections.values());
    
    const metrics: SSEHealthMetrics = {
      totalConnections: connections.length,
      healthyConnections: connections.filter(c => c.isHealthy).length,
      unhealthyConnections: connections.filter(c => !c.isHealthy).length,
      averageLatency: connections.reduce((sum, c) => sum + c.latency, 0) / connections.length || 0,
      totalErrors: connections.reduce((sum, c) => sum + c.errorCount, 0),
      totalReconnects: connections.reduce((sum, c) => sum + c.reconnectCount, 0),
      connectionsByRole: {},
      connectionsByStatus: {
        connected: 0,
        disconnected: 0,
        error: 0
      }
    };

    // Count by role
    connections.forEach(connection => {
      metrics.connectionsByRole[connection.userRole] = 
        (metrics.connectionsByRole[connection.userRole] || 0) + 1;
      
      // Count by status
      if (connection.isHealthy) {
        metrics.connectionsByStatus.connected++;
      } else if (connection.errorCount > 0) {
        metrics.connectionsByStatus.error++;
      } else {
        metrics.connectionsByStatus.disconnected++;
      }
    });

    return metrics;
  }

  /**
   * Get connection health details
   */
  getConnectionHealth(connectionId: string): SSEConnectionHealth | null {
    return this.connections.get(connectionId) || null;
  }

  /**
   * Get all connection health details
   */
  getAllConnectionsHealth(): SSEConnectionHealth[] {
    return Array.from(this.connections.values());
  }

  /**
   * Send heartbeat to all connections
   */
  async sendHeartbeatToAll(): Promise<void> {
    const connections = Array.from(this.connections.values());
    const heartbeatPromises = connections.map(async (connection) => {
      try {
        // This would typically send a heartbeat message to the SSE connection
        // For now, we'll just update the heartbeat timestamp
        await this.recordHeartbeat(connection.connectionId);
      } catch (error) {
        await this.recordError(connection.connectionId, `Heartbeat failed: ${error}`);
      }
    });

    await Promise.allSettled(heartbeatPromises);
  }

  /**
   * Perform health check on all connections
   */
  async performHealthCheck(): Promise<void> {
    const now = new Date();
    const unhealthyConnections: string[] = [];

    for (const [connectionId, connection] of this.connections.entries()) {
      const timeSinceLastActivity = now.getTime() - connection.lastActivity.getTime();
      
      if (timeSinceLastActivity > this.CONNECTION_TIMEOUT) {
        connection.isHealthy = false;
        unhealthyConnections.push(connectionId);
        
        Logger.warn(`⚠️ SSE connection timeout: ${connectionId} - Last activity: ${connection.lastActivity}`);
      }
    }

    // Clean up timed-out connections
    for (const connectionId of unhealthyConnections) {
      await this.unregisterConnection(connectionId);
    }

    if (unhealthyConnections.length > 0) {
      Logger.info(`🧹 Cleaned up ${unhealthyConnections.length} timed-out SSE connections`);
    }
  }

  /**
   * Start monitoring processes
   */
  private startMonitoring(): void {
    // Start heartbeat interval
    this.heartbeatInterval = setInterval(async () => {
      await this.sendHeartbeatToAll();
    }, this.HEARTBEAT_INTERVAL);

    // Start health check interval
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * Stop monitoring processes
   */
  stopMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    Logger.info('📊 SSE Health Monitor stopped');
  }

  /**
   * Calculate health status based on connection metrics
   */
  private calculateHealthStatus(connection: SSEConnectionHealth): boolean {
    const now = new Date();
    const timeSinceLastActivity = now.getTime() - connection.lastActivity.getTime();
    
    // Connection is unhealthy if:
    // - No activity for more than timeout period
    // - Too many errors (more than 10)
    // - High latency (more than 5 seconds)
    return timeSinceLastActivity < this.CONNECTION_TIMEOUT &&
           connection.errorCount < 10 &&
           connection.latency < 5000;
  }

  /**
   * Persist connection health to Redis
   */
  private async persistConnectionHealth(connection: SSEConnectionHealth): Promise<void> {
    try {
      const key = `${this.REDIS_KEY_PREFIX}${connection.connectionId}`;
      const ttl = 24 * 60 * 60; // 24 hours
      
      await redisOperations.setex(key, ttl, JSON.stringify(connection));
    } catch (error) {
      Logger.error('Failed to persist SSE connection health:', error);
    }
  }
}

// Export singleton instance
export const sseHealthMonitor = new SSEHealthMonitor();
